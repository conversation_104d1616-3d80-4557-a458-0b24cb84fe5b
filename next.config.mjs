import bundleAnalyzer from "@next/bundle-analyzer";
import createNextIntlPlugin from "next-intl/plugin";
import mdx from "@next/mdx";

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const withNextIntl = createNextIntlPlugin();

const withMDX = mdx({
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "*",
      },
    ],
  },
  // ISR 配置
  experimental: {
    // Next.js 15+ 中 isrMemoryCacheSize 已被移除
    // ISR 现在由 Cloudflare 自动处理
  },
  // 静态生成配置
  generateBuildId: async () => {
    // 使用时间戳作为构建ID，确保每次构建都有唯一ID
    return `pinpoint-${Date.now()}`;
  },
  async redirects() {
    return [];
  },
  // 重写规则，支持 Pinpoint URL 格式
  async rewrites() {
    return [
      {
        source: '/linkedin-pinpoint-:gameNumber(\\d+)-:clues*',
        destination: '/answer/:gameNumber/:clues*',
      },
    ];
  },
};

// Make sure experimental mdx flag is enabled
const configWithMDX = {
  ...nextConfig,
  experimental: {
    ...nextConfig.experimental,
    mdxRs: true,
  },
};

export default withBundleAnalyzer(withNextIntl(withMDX(configWithMDX)));

import { initOpenNextCloudflareForDev } from "@opennextjs/cloudflare";
initOpenNextCloudflareForDev();
