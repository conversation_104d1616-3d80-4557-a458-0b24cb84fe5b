# GitHub Actions 定时爬虫设置指南

## 📋 概述

使用 GitHub Actions 每天自动运行 Pinpoint 爬虫，将数据存储到 Supabase。

## 🔧 设置步骤

### 1. 配置 GitHub Secrets

在你的 GitHub 仓库中设置以下 Secrets：

1. 进入仓库 → Settings → Secrets and variables → Actions
2. 点击 "New repository secret" 添加以下变量：

```
SUPABASE_URL = https://urjvwlqymdokurivceni.supabase.co
SUPABASE_SERVICE_ROLE_KEY = 你的_service_role_key
```

### 2. 文件结构

确保以下文件存在：

```
├── .github/workflows/pinpoint-crawler.yml  # GitHub Actions 配置
├── app/scripts/pinpoint_crawler.py          # 爬虫脚本
├── requirements.txt                         # Python 依赖
└── docs/github-actions-setup.md            # 本文档
```

### 3. 定时执行

- **自动执行**: 每天 UTC 15:00 (对应美国东部时间上午)
- **手动执行**: 在 Actions 页面可以手动触发

## 🚀 部署

1. **提交代码**:
   ```bash
   git add .
   git commit -m "Add GitHub Actions pinpoint crawler"
   git push
   ```

2. **验证部署**:
   - 进入 GitHub 仓库 → Actions 页面
   - 查看 "Pinpoint Daily Crawler" workflow
   - 可以点击 "Run workflow" 手动测试

## 📊 监控

### 查看执行日志
1. 进入 Actions 页面
2. 点击具体的 workflow run
3. 查看详细的执行日志

### 执行状态
- ✅ **成功**: 数据已成功爬取并存储
- ❌ **失败**: 检查日志查看具体错误信息

## 🔍 故障排除

### 常见问题

1. **Secrets 未配置**
   - 确保 `SUPABASE_URL` 和 `SUPABASE_SERVICE_ROLE_KEY` 已正确设置

2. **依赖安装失败**
   - 检查 `requirements.txt` 文件是否存在
   - 确保依赖版本兼容

3. **网络请求失败**
   - Pinpoint 网站可能暂时不可用
   - 检查目标网站是否更改了结构

4. **数据库连接失败**
   - 验证 Supabase 凭据是否正确
   - 检查 Supabase 服务状态

### 调试方法

1. **手动触发测试**:
   - 在 Actions 页面点击 "Run workflow"
   - 查看实时日志输出

2. **本地测试**:
   ```bash
   # 设置环境变量
   export SUPABASE_URL="your_url"
   export SUPABASE_SERVICE_ROLE_KEY="your_key"
   
   # 运行脚本
   python app/scripts/pinpoint_crawler.py
   ```

## 💡 优势

- ✅ **免费**: GitHub Actions 提供免费的执行时间
- ✅ **可靠**: 基于 GitHub 的基础设施
- ✅ **简单**: 无需额外的服务器或配置
- ✅ **透明**: 完整的执行日志和历史记录
- ✅ **灵活**: 可以随时调整执行时间和逻辑

## ⚙️ 自定义配置

### 修改执行时间

编辑 `.github/workflows/pinpoint-crawler.yml` 中的 cron 表达式：

```yaml
schedule:
  - cron: '0 15 * * *'  # 每天 UTC 15:00
```

### 添加通知

可以添加 Slack、Discord 或邮件通知：

```yaml
- name: Notify on failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## 📈 扩展功能

1. **多数据源**: 可以扩展爬取其他游戏网站
2. **数据验证**: 添加更严格的数据验证逻辑
3. **缓存机制**: 避免重复爬取相同数据
4. **错误重试**: 添加自动重试机制
