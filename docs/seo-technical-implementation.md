# SEO 技术实施指南

## 🔧 结构化数据实施

### 1. JsonLd 组件创建

```typescript
// components/seo/JsonLd.tsx
interface JsonLdProps {
  data: Record<string, any>;
}

export default function JsonLd({ data }: JsonLdProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}
```

### 2. 网站级别 Schema (首页)

```typescript
// lib/seo/schemas.ts
export function getWebsiteSchema(locale: string) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
  
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": locale === "zh" ? "LinkedIn Pinpoint 每日答案" : "LinkedIn Pinpoint Daily Answers",
    "description": locale === "zh" 
      ? "提供 LinkedIn Pinpoint 游戏的每日答案、线索解析和游戏攻略"
      : "Daily answers, clues analysis and game guides for LinkedIn Pinpoint",
    "url": baseUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Pinpoint Today",
      "url": baseUrl
    }
  };
}
```

### 3. 文章级别 Schema (答案详情页)

```typescript
export function getArticleSchema(answer: any, locale: string) {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL;
  
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": `LinkedIn Pinpoint #${answer.game_number} Answer: ${answer.answer}`,
    "description": `The answer to LinkedIn Pinpoint #${answer.game_number} is "${answer.answer}". Clues: ${answer.clues.join(', ')}`,
    "image": `${baseUrl}/og-image.png`,
    "author": {
      "@type": "Organization",
      "name": "Pinpoint Today"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Pinpoint Today",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`
      }
    },
    "datePublished": answer.date,
    "dateModified": answer.updated_at || answer.date,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${baseUrl}/${answer.url_slug}`
    },
    "articleSection": "Gaming",
    "keywords": [
      "LinkedIn Pinpoint",
      `Pinpoint ${answer.game_number}`,
      answer.answer,
      ...answer.clues
    ].join(", ")
  };
}
```

### 4. 面包屑 Schema

```typescript
export function getBreadcrumbSchema(items: Array<{name: string, url: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
}
```

## 🧭 面包屑导航实施

### 1. 面包屑组件

```typescript
// components/seo/Breadcrumb.tsx
import Link from 'next/link';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  name: string;
  href?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

export default function Breadcrumb({ items }: BreadcrumbProps) {
  return (
    <nav aria-label="Breadcrumb" className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
      <Link href="/" className="flex items-center hover:text-blue-600">
        <Home className="w-4 h-4" />
        <span className="sr-only">首页</span>
      </Link>
      
      {items.map((item, index) => (
        <div key={index} className="flex items-center space-x-2">
          <ChevronRight className="w-4 h-4 text-gray-400" />
          {item.href ? (
            <Link href={item.href} className="hover:text-blue-600">
              {item.name}
            </Link>
          ) : (
            <span className="text-gray-900 font-medium">{item.name}</span>
          )}
        </div>
      ))}
    </nav>
  );
}
```

### 2. 面包屑使用示例

```typescript
// 在答案详情页中使用
const breadcrumbItems = [
  { name: "历史答案", href: "/history" },
  { name: `第 ${answer.game_number} 期` }
];

return (
  <div>
    <Breadcrumb items={breadcrumbItems} />
    <JsonLd data={getBreadcrumbSchema([
      { name: "首页", url: baseUrl },
      { name: "历史答案", url: `${baseUrl}/history` },
      { name: `第 ${answer.game_number} 期`, url: `${baseUrl}/${answer.url_slug}` }
    ])} />
    {/* 页面内容 */}
  </div>
);
```

## 🔍 搜索功能实施

### 1. 搜索页面结构

```typescript
// app/search/page.tsx
import { Suspense } from 'react';
import SearchResults from '@/components/search/SearchResults';
import SearchBox from '@/components/search/SearchBox';

export async function generateMetadata({ searchParams }: { searchParams: { q?: string } }) {
  const query = searchParams.q || '';
  
  return {
    title: query ? `搜索 "${query}" - LinkedIn Pinpoint` : '搜索 - LinkedIn Pinpoint',
    description: '搜索 LinkedIn Pinpoint 历史答案和游戏攻略',
    robots: { index: false } // 搜索结果页面不需要被索引
  };
}

export default function SearchPage({ searchParams }: { searchParams: { q?: string } }) {
  const query = searchParams.q || '';
  
  return (
    <div className="container mx-auto px-6 py-8">
      <h1 className="text-3xl font-bold mb-8">搜索答案</h1>
      
      <SearchBox defaultValue={query} />
      
      <Suspense fallback={<div>搜索中...</div>}>
        <SearchResults query={query} />
      </Suspense>
    </div>
  );
}
```

### 2. 搜索组件

```typescript
// components/search/SearchBox.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search } from 'lucide-react';

interface SearchBoxProps {
  defaultValue?: string;
}

export default function SearchBox({ defaultValue = '' }: SearchBoxProps) {
  const [query, setQuery] = useState(defaultValue);
  const router = useRouter();
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`/search?q=${encodeURIComponent(query.trim())}`);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="mb-8">
      <div className="relative max-w-md">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="搜索答案、线索词..."
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
      </div>
    </form>
  );
}
```

## 📊 性能优化实施

### 1. 图片优化

```typescript
// components/ui/OptimizedImage.tsx
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  className?: string;
}

export default function OptimizedImage({ 
  src, 
  alt, 
  width, 
  height, 
  priority = false,
  className 
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={priority}
      className={className}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      quality={85}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
    />
  );
}
```

### 2. 代码分割

```typescript
// 动态导入重型组件
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('@/components/HeavyComponent'), {
  loading: () => <div>加载中...</div>,
  ssr: false // 如果不需要服务端渲染
});
```

### 3. 缓存策略

```typescript
// next.config.mjs 中添加
const nextConfig = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, s-maxage=300, stale-while-revalidate=600'
          }
        ]
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          }
        ]
      }
    ];
  }
};
```

## 📱 移动端优化

### 1. 响应式设计检查

```css
/* 确保所有组件都有适当的移动端样式 */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .text-hero {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
```

### 2. 触摸友好的交互

```typescript
// 确保按钮有足够的触摸区域
const buttonClass = "min-h-[44px] min-w-[44px] px-4 py-2";
```

## 🔗 内部链接优化

### 1. 相关答案推荐

```typescript
// components/content/RelatedAnswers.tsx
interface RelatedAnswersProps {
  currentAnswer: any;
  relatedAnswers: any[];
}

export default function RelatedAnswers({ currentAnswer, relatedAnswers }: RelatedAnswersProps) {
  return (
    <section className="mt-12">
      <h2 className="text-2xl font-bold mb-6">相关答案</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {relatedAnswers.map((answer) => (
          <Link 
            key={answer.id} 
            href={`/${answer.url_slug}`}
            className="block p-4 border rounded-lg hover:shadow-md transition-shadow"
          >
            <h3 className="font-semibold">第 {answer.game_number} 期</h3>
            <p className="text-gray-600">{answer.date}</p>
            <p className="text-sm text-gray-500 mt-2">
              线索: {answer.clues.slice(0, 3).join(', ')}...
            </p>
          </Link>
        ))}
      </div>
    </section>
  );
}
```

### 2. 智能内部链接

```typescript
// lib/seo/internal-links.ts
export function generateInternalLinks(content: string, allAnswers: any[]) {
  // 自动为内容中的关键词添加内部链接
  let linkedContent = content;
  
  allAnswers.forEach(answer => {
    const regex = new RegExp(`\\b${answer.answer}\\b`, 'gi');
    linkedContent = linkedContent.replace(regex, 
      `<a href="/${answer.url_slug}" class="text-blue-600 hover:underline">${answer.answer}</a>`
    );
  });
  
  return linkedContent;
}
```

---

**实施顺序建议**:
1. 先实施结构化数据和面包屑导航
2. 然后优化页面性能和移动端体验
3. 最后添加搜索功能和高级内部链接
4. 持续监控和优化效果
