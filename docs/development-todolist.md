# LinkedIn Pinpoint 每日答案网站 - 开发 TODOLIST

## 📋 总体开发计划 (5天完成)

### 🎯 MVP目标
- ✅ 今日答案卡片展示
- ✅ 倒计时器功能  
- ✅ 历史答案查询
- ✅ 管理后台录入
- ✅ 动态路由页面

---

## 📅 第1天：数据库设计和基础架构

### ✅ 任务清单

#### 1. 扩展数据库表结构
- [ ] 在 `data/install.sql` 末尾添加 pinpoint 表
- [ ] 执行数据库迁移
- [ ] 验证表创建成功

```sql
-- 添加到 data/install.sql
CREATE TABLE pinpoint_daily_answers (
  id SERIAL PRIMARY KEY,
  game_number INTEGER UNIQUE NOT NULL,
  date DATE UNIQUE NOT NULL,
  answer VARCHAR(255) NOT NULL,
  clue_word_1 VARCHAR(100) NOT NULL,
  clue_word_2 VARCHAR(100) NOT NULL,
  clue_word_3 VARCHAR(100) NOT NULL,
  clue_word_4 VARCHAR(100) NOT NULL,
  clue_word_5 VARCHAR(100) NOT NULL,
  url_slug VARCHAR(500) NOT NULL,
  created_at timestamptz DEFAULT NOW(),
  updated_at timestamptz DEFAULT NOW(),
  status VARCHAR(50) DEFAULT 'published'
);

CREATE INDEX idx_pinpoint_game_number ON pinpoint_daily_answers(game_number);
CREATE INDEX idx_pinpoint_date ON pinpoint_daily_answers(date DESC);
CREATE INDEX idx_pinpoint_url_slug ON pinpoint_daily_answers(url_slug);
```

#### 2. 创建类型定义
- [ ] 创建 `types/pinpoint.d.ts`
- [ ] 定义所有接口类型

#### 3. 创建数据模型
- [ ] 创建 `models/pinpoint.ts`
- [ ] 实现 CRUD 操作函数

#### 4. 创建工具函数
- [ ] 创建 `lib/pinpoint.ts`
- [ ] 实现 URL slug 生成
- [ ] 实现数据验证函数
- [ ] 实现倒计时计算函数

#### 5. 测试数据库连接
- [ ] 验证 Supabase 连接正常
- [ ] 测试基础 CRUD 操作

**预计时间**: 4-6小时

---

## 📅 第2天：管理后台开发

### ✅ 任务清单

#### 1. 扩展管理后台导航
- [ ] 修改 `app/[locale]/(admin)/layout.tsx`
- [ ] 添加 Pinpoint 菜单项

```typescript
// 在 sidebar.library.items 中添加
{
  title: "Pinpoint",
  url: "/admin/pinpoint",
  icon: "RiGamepadLine",
}
```

#### 2. 创建管理后台API
- [ ] 创建 `app/api/admin/pinpoint/route.ts`
- [ ] 实现 POST (新增) 和 GET (列表) 接口
- [ ] 创建 `app/api/admin/pinpoint/[id]/route.ts`
- [ ] 实现 PUT (更新) 和 DELETE (删除) 接口

#### 3. 创建答案列表页面
- [ ] 创建 `app/[locale]/(admin)/admin/pinpoint/page.tsx`
- [ ] 显示所有答案列表
- [ ] 添加搜索和分页功能

#### 4. 创建答案新增页面
- [ ] 创建 `app/[locale]/(admin)/admin/pinpoint/new/page.tsx`
- [ ] 实现表单组件
- [ ] 添加实时预览功能

#### 5. 创建答案编辑页面
- [ ] 创建 `app/[locale]/(admin)/admin/pinpoint/[id]/page.tsx`
- [ ] 支持修改和删除操作

#### 6. 添加表单验证和错误处理
- [ ] 前端表单验证
- [ ] 错误提示组件
- [ ] 成功反馈提示

**预计时间**: 6-8小时

---

## 📅 第3天：前端API接口开发

### ✅ 任务清单

#### 1. 创建今日答案API
- [ ] 创建 `app/api/pinpoint/today/route.ts`
- [ ] 返回今日答案数据
- [ ] 处理无答案情况

#### 2. 创建历史答案API
- [ ] 创建 `app/api/pinpoint/history/route.ts`
- [ ] 支持分页查询
- [ ] 支持日期筛选

#### 3. 创建单个答案API
- [ ] 创建 `app/api/pinpoint/[slug]/route.ts`
- [ ] 根据 URL slug 返回答案详情
- [ ] 处理 404 情况

#### 4. 添加API缓存机制
- [ ] 设置适当的缓存头
- [ ] 今日答案缓存1小时
- [ ] 历史答案缓存24小时

#### 5. 添加API错误处理
- [ ] 统一错误响应格式
- [ ] 添加日志记录
- [ ] 处理数据库连接错误

#### 6. 测试API接口
- [ ] 使用 Postman 测试所有接口
- [ ] 验证数据格式正确性
- [ ] 测试边界情况

**预计时间**: 4-5小时

---

## 📅 第4天：前端页面开发

### ✅ 任务清单

#### 1. 创建首页组件
- [ ] 创建 `components/pinpoint/TodayAnswerCard.tsx`
- [ ] 显示游戏编号、线索词、答案
- [ ] 添加精美的卡片样式

#### 2. 创建倒计时器组件
- [ ] 创建 `components/pinpoint/CountdownTimer.tsx`
- [ ] 实时显示剩余时间
- [ ] 自动刷新功能

#### 3. 创建首页页面
- [ ] 修改或创建主页面
- [ ] 集成今日答案卡片
- [ ] 集成倒计时器
- [ ] 添加快速导航

#### 4. 创建历史答案页面
- [ ] 创建 `/history` 页面
- [ ] 显示历史答案列表
- [ ] 实现分页功能
- [ ] 添加搜索功能

#### 5. 创建动态路由页面
- [ ] 创建 `[slug].tsx` 动态路由
- [ ] 显示单个答案详情
- [ ] 添加面包屑导航
- [ ] 添加分享功能

#### 6. 添加响应式设计
- [ ] 优化移动端显示
- [ ] 调整平板端布局
- [ ] 测试各种屏幕尺寸

#### 7. 添加加载状态
- [ ] 页面加载骨架屏
- [ ] API 请求加载状态
- [ ] 错误状态处理

**预计时间**: 6-8小时

---

## 📅 第5天：测试、优化和部署

### ✅ 任务清单

#### 1. 功能测试
- [ ] 测试答案录入流程
- [ ] 测试前端显示功能
- [ ] 测试历史查询功能
- [ ] 测试动态路由功能
- [ ] 测试倒计时器准确性

#### 2. SEO优化
- [ ] 添加页面 meta 标签
- [ ] 生成 sitemap.xml
- [ ] 优化 robots.txt
- [ ] 添加结构化数据

#### 3. 性能优化
- [ ] 优化图片加载
- [ ] 代码分割优化
- [ ] 压缩静态资源
- [ ] 添加 CDN 配置

#### 4. 错误处理完善
- [ ] 创建 404 页面
- [ ] 添加错误边界组件
- [ ] 完善用户友好的错误提示
- [ ] 添加错误日志收集

#### 5. 跨浏览器测试
- [ ] Chrome 测试
- [ ] Firefox 测试
- [ ] Safari 测试
- [ ] 移动端浏览器测试

#### 6. 部署到Vercel
- [ ] 配置环境变量
- [ ] 连接 GitHub 仓库
- [ ] 设置自动部署
- [ ] 验证生产环境功能

#### 7. 域名配置
- [ ] 购买域名 (可选)
- [ ] 配置 DNS 解析
- [ ] 设置 SSL 证书
- [ ] 测试域名访问

#### 8. 监控设置
- [ ] 设置 Uptime Robot 监控
- [ ] 配置 Google Analytics
- [ ] 设置错误追踪 (Sentry)
- [ ] 配置性能监控

**预计时间**: 6-8小时

---

## 🚀 快速启动指南

### 立即开始 (今天就能做的)

1. **准备环境**
   ```bash
   # 确保依赖已安装
   pnpm install
   
   # 检查 Supabase 连接
   # 确保 .env.local 中有正确的 SUPABASE_URL 和 SUPABASE_ANON_KEY
   ```

2. **创建数据库表**
   - 登录 Supabase Dashboard
   - 执行 SQL 创建 pinpoint_daily_answers 表

3. **开始第一天的任务**
   - 创建类型定义文件
   - 创建数据模型文件
   - 测试数据库连接

### 每日检查清单

- [ ] 完成当天所有任务
- [ ] 提交代码到 Git
- [ ] 更新任务状态
- [ ] 记录遇到的问题和解决方案

### 风险控制

- **数据备份**: 每天备份数据库
- **代码版本**: 每个功能完成后提交 Git
- **测试验证**: 每个 API 完成后立即测试
- **文档更新**: 及时更新 README 和部署文档

---

## 📞 需要帮助时

如果在开发过程中遇到问题，可以：

1. 查看现有代码结构和模式
2. 参考 `models/user.ts` 等现有模型文件
3. 查看 `app/api/ping/route.ts` 等现有 API 示例
4. 检查 Supabase 连接和权限设置

**预计总开发时间**: 26-35小时 (5个工作日)
**每日更新维护时间**: 2-3分钟
**总项目成本**: < $20/年
