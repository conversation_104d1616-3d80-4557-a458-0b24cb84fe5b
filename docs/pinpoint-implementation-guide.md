# Pinpoint 实现指南 - 基于现有架构

## 1. 数据库扩展

### 1.1 更新 data/install.sql
```sql
-- 在现有install.sql文件末尾添加
CREATE TABLE pinpoint_daily_answers (
  id SERIAL PRIMARY KEY,
  game_number INTEGER UNIQUE NOT NULL,
  date DATE UNIQUE NOT NULL,
  answer VARCHAR(255) NOT NULL,
  clue_word_1 VARCHAR(100) NOT NULL,
  clue_word_2 VARCHAR(100) NOT NULL,
  clue_word_3 VARCHAR(100) NOT NULL,
  clue_word_4 VARCHAR(100) NOT NULL,
  clue_word_5 VARCHAR(100) NOT NULL,
  url_slug VARCHAR(500) NOT NULL,
  created_at timestamptz DEFAULT NOW(),
  updated_at timestamptz DEFAULT NOW(),
  status VARCHAR(50) DEFAULT 'published'
);

CREATE INDEX idx_pinpoint_game_number ON pinpoint_daily_answers(game_number);
CREATE INDEX idx_pinpoint_date ON pinpoint_daily_answers(date DESC);
CREATE INDEX idx_pinpoint_url_slug ON pinpoint_daily_answers(url_slug);
```

## 2. 类型定义

### 2.1 创建 types/pinpoint.d.ts
```typescript
export interface PinpointAnswer {
  id?: number;
  game_number: number;
  date: string;
  answer: string;
  clue_word_1: string;
  clue_word_2: string;
  clue_word_3: string;
  clue_word_4: string;
  clue_word_5: string;
  url_slug: string;
  created_at?: string;
  updated_at?: string;
  status?: 'published' | 'draft' | 'deleted';
}

export interface PinpointFormData {
  gameNumber: number;
  date: string;
  answer: string;
  clueWords: [string, string, string, string, string];
}

export interface PinpointListResponse {
  data: PinpointAnswer[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

## 3. 数据模型

### 3.1 创建 models/pinpoint.ts
```typescript
import { PinpointAnswer } from "@/types/pinpoint";
import { getSupabaseClient } from "./db";

export async function insertPinpointAnswer(answer: PinpointAnswer) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .insert(answer)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function findPinpointAnswerByGameNumber(
  gameNumber: number
): Promise<PinpointAnswer | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("game_number", gameNumber)
    .eq("status", "published")
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function findPinpointAnswerBySlug(
  slug: string
): Promise<PinpointAnswer | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("url_slug", slug)
    .eq("status", "published")
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function getTodayPinpointAnswer(): Promise<PinpointAnswer | null> {
  const today = new Date().toISOString().split('T')[0];
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("date", today)
    .eq("status", "published")
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function getPinpointAnswersHistory(
  page: number = 1,
  limit: number = 20
) {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  const { data, error, count } = await supabase
    .from("pinpoint_daily_answers")
    .select("*", { count: "exact" })
    .eq("status", "published")
    .order("date", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return {
    data: data || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
    },
  };
}

export async function updatePinpointAnswer(
  id: number,
  updates: Partial<PinpointAnswer>
) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function deletePinpointAnswer(id: number) {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("pinpoint_daily_answers")
    .update({ status: "deleted" })
    .eq("id", id);

  if (error) {
    throw error;
  }

  return true;
}
```

## 4. 工具函数

### 4.1 创建 lib/pinpoint.ts
```typescript
export function generateUrlSlug(gameNumber: number, clueWords: string[]): string {
  const cleanWords = clueWords.map(word => 
    word.toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  );
  
  return `linkedin-pinpoint-${gameNumber}-${cleanWords.join('-')}`;
}

export function validatePinpointData(data: any): string[] {
  const errors: string[] = [];

  if (!data.gameNumber || data.gameNumber < 1) {
    errors.push("游戏编号必须是正整数");
  }

  if (!data.date) {
    errors.push("日期不能为空");
  }

  if (!data.answer || data.answer.trim().length === 0) {
    errors.push("答案不能为空");
  }

  if (!data.clueWords || data.clueWords.length !== 5) {
    errors.push("必须提供5个线索词");
  } else {
    data.clueWords.forEach((word: string, index: number) => {
      if (!word || word.trim().length === 0) {
        errors.push(`线索词 ${index + 1} 不能为空`);
      }
    });
  }

  return errors;
}

export function getNextPuzzleTime(): Date {
  const now = new Date();
  const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));
  
  // 下一个午夜（太平洋时间）
  const nextMidnight = new Date(pacificTime);
  nextMidnight.setDate(nextMidnight.getDate() + 1);
  nextMidnight.setHours(0, 0, 0, 0);
  
  return nextMidnight;
}

export function formatTimeRemaining(targetTime: Date): string {
  const now = new Date();
  const diff = targetTime.getTime() - now.getTime();
  
  if (diff <= 0) {
    return "00:00:00";
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}
```

## 5. API 接口

### 5.1 今日答案 API - app/api/pinpoint/today/route.ts
```typescript
import { getTodayPinpointAnswer } from "@/models/pinpoint";
import { respData, respErr } from "@/lib/resp";

export async function GET() {
  try {
    const todayAnswer = await getTodayPinpointAnswer();
    
    if (!todayAnswer) {
      return respErr("今日答案暂未发布");
    }

    const response = {
      gameNumber: todayAnswer.game_number,
      date: todayAnswer.date,
      answer: todayAnswer.answer,
      clues: [
        todayAnswer.clue_word_1,
        todayAnswer.clue_word_2,
        todayAnswer.clue_word_3,
        todayAnswer.clue_word_4,
        todayAnswer.clue_word_5,
      ],
      urlSlug: todayAnswer.url_slug,
    };

    return respData(response);
  } catch (error) {
    console.error("获取今日答案失败:", error);
    return respErr("获取今日答案失败");
  }
}
```

### 5.2 历史答案 API - app/api/pinpoint/history/route.ts
```typescript
import { getPinpointAnswersHistory } from "@/models/pinpoint";
import { respData, respErr } from "@/lib/resp";

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const result = await getPinpointAnswersHistory(page, limit);

    const formattedData = result.data.map(item => ({
      gameNumber: item.game_number,
      date: item.date,
      answer: item.answer,
      clues: [
        item.clue_word_1,
        item.clue_word_2,
        item.clue_word_3,
        item.clue_word_4,
        item.clue_word_5,
      ],
      urlSlug: item.url_slug,
    }));

    return respData({
      data: formattedData,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error("获取历史答案失败:", error);
    return respErr("获取历史答案失败");
  }
}
```

### 5.3 单个答案 API - app/api/pinpoint/[slug]/route.ts
```typescript
import { findPinpointAnswerBySlug } from "@/models/pinpoint";
import { respData, respErr } from "@/lib/resp";

export async function GET(
  req: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const answer = await findPinpointAnswerBySlug(params.slug);
    
    if (!answer) {
      return respErr("答案不存在");
    }

    const response = {
      gameNumber: answer.game_number,
      date: answer.date,
      answer: answer.answer,
      clues: [
        answer.clue_word_1,
        answer.clue_word_2,
        answer.clue_word_3,
        answer.clue_word_4,
        answer.clue_word_5,
      ],
      urlSlug: answer.url_slug,
    };

    return respData(response);
  } catch (error) {
    console.error("获取答案详情失败:", error);
    return respErr("获取答案详情失败");
  }
}
```

## 6. 管理后台 API

### 6.1 管理 API - app/api/admin/pinpoint/route.ts
```typescript
import { 
  insertPinpointAnswer, 
  getPinpointAnswersHistory 
} from "@/models/pinpoint";
import { generateUrlSlug, validatePinpointData } from "@/lib/pinpoint";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("未授权");
    }

    const body = await req.json();
    const errors = validatePinpointData(body);
    
    if (errors.length > 0) {
      return respErr(errors.join(", "));
    }

    const urlSlug = generateUrlSlug(body.gameNumber, body.clueWords);
    
    const pinpointAnswer = {
      game_number: body.gameNumber,
      date: body.date,
      answer: body.answer,
      clue_word_1: body.clueWords[0],
      clue_word_2: body.clueWords[1],
      clue_word_3: body.clueWords[2],
      clue_word_4: body.clueWords[3],
      clue_word_5: body.clueWords[4],
      url_slug: urlSlug,
      status: "published" as const,
    };

    const result = await insertPinpointAnswer(pinpointAnswer);
    return respData(result);
  } catch (error) {
    console.error("创建答案失败:", error);
    return respErr("创建答案失败");
  }
}

export async function GET(req: Request) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("未授权");
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const result = await getPinpointAnswersHistory(page, limit);
    return respData(result);
  } catch (error) {
    console.error("获取答案列表失败:", error);
    return respErr("获取答案列表失败");
  }
}
```

## 7. 下一步实现

### 7.1 立即可实现 (1天)
1. 执行数据库迁移 (添加表)
2. 创建上述文件
3. 在管理后台添加Pinpoint菜单项
4. 创建基础的录入表单

### 7.2 完整功能 (3-4天)
1. 管理后台完整界面
2. 前端展示页面
3. 倒计时器组件
4. 响应式设计优化

### 7.3 优化功能 (1周)
1. 缓存机制
2. SEO优化
3. 错误处理完善
4. 性能优化

这个方案基于你现有的架构，可以快速实现，每天只需2-3分钟手动录入，成本极低且可靠性高。
