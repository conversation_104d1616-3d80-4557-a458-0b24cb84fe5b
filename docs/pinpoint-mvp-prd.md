# LinkedIn Pinpoint 每日答案网站 - MVP版本 PRD

## 1. 产品概述

### 1.1 MVP目标
创建一个简洁的LinkedIn Pinpoint每日答案查询网站，专注于核心功能：
- 展示每日答案
- 提供历史答案查询
- 简洁的用户体验

### 1.2 目标用户
- LinkedIn Pinpoint 游戏玩家
- 寻找每日答案的用户

### 1.3 核心价值
- 快速获取准确答案
- 简单易用的界面
- 及时的内容更新

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 首页 (/)
**功能描述**: 展示今日Pinpoint答案
**详细需求**:
- 今日答案卡片
  - 游戏编号 (如 #455)
  - 5个线索词汇展示
  - 正确答案/类别
  - 发布日期
- 倒计时器
  - 显示距离下一个谜题发布的剩余时间
  - 太平洋时间午夜重置
- 快速导航
  - 历史答案链接
  - 简洁的导航菜单

#### 2.1.2 历史答案页 (/history)
**功能描述**: 查看历史答案列表
**详细需求**:
- 答案列表展示
  - 按日期倒序排列
  - 显示游戏编号、日期、答案
  - 点击查看详情
- 简单分页
  - 每页显示20条记录
  - 上一页/下一页导航

#### 2.1.3 答案详情页 (/linkedin-pinpoint-[number]-[clues])
**功能描述**: 单个答案的详细页面
**详细需求**:
- URL格式: `/linkedin-pinpoint-455-book-point-list-mate-please`
  - 455: 游戏编号
  - book-point-list-mate-please: 5个线索词用连字符连接
- 页面内容:
  - 游戏编号和日期
  - 5个线索词汇
  - 正确答案
  - 返回首页/历史页面链接

## 3. 技术架构

### 3.1 技术栈
- **前端**: Next.js 14+ (已有)
- **样式**: Tailwind CSS (已有)
- **UI组件**: Radix UI (已有)
- **数据库**: 基于现有数据库系统
- **部署**: Vercel (推荐)

### 3.2 数据库设计

```sql
-- 简化的答案表
CREATE TABLE pinpoint_daily_answers (
  id BIGINT PRIMARY KEY,
  game_number INTEGER UNIQUE NOT NULL,
  date DATE UNIQUE NOT NULL,
  answer VARCHAR(255) NOT NULL,
  clue_word_1 VARCHAR(100) NOT NULL,
  clue_word_2 VARCHAR(100) NOT NULL,
  clue_word_3 VARCHAR(100) NOT NULL,
  clue_word_4 VARCHAR(100) NOT NULL,
  clue_word_5 VARCHAR(100) NOT NULL,
  url_slug VARCHAR(500) NOT NULL, -- linkedin-pinpoint-455-book-point-list-mate-please
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_game_number ON pinpoint_daily_answers(game_number);
CREATE INDEX idx_date ON pinpoint_daily_answers(date);
CREATE INDEX idx_url_slug ON pinpoint_daily_answers(url_slug);
```

### 3.3 API设计

#### 3.3.1 获取今日答案
```
GET /api/today
Response: {
  gameNumber: 455,
  date: "2025-08-01",
  answer: "Words that come after 'head'",
  clues: ["book", "point", "list", "mate", "please"],
  urlSlug: "linkedin-pinpoint-455-book-point-list-mate-please"
}
```

#### 3.3.2 获取历史答案
```
GET /api/history?page=1&limit=20
Response: {
  data: [...],
  pagination: {
    page: 1,
    limit: 20,
    total: 100,
    totalPages: 5
  }
}
```

#### 3.3.3 获取单个答案
```
GET /api/answer/[slug]
Response: { ... }
```

## 4. 页面设计

### 4.1 首页布局
```
┌─────────────────────────────────────┐
│              导航栏                  │
├─────────────────────────────────────┤
│                                     │
│         今日答案卡片                 │
│    ┌─────────────────────────┐      │
│    │  Pinpoint #455          │      │
│    │  2025-08-01             │      │
│    │                         │      │
│    │  线索词: book, point,   │      │
│    │  list, mate, please     │      │
│    │                         │      │
│    │  答案: Words that come  │      │
│    │  after "head"           │      │
│    └─────────────────────────┘      │
│                                     │
│         倒计时器                     │
│    下一题发布: 23:45:12             │
│                                     │
│    [查看历史答案]                   │
│                                     │
└─────────────────────────────────────┘
```

### 4.2 历史答案页布局
```
┌─────────────────────────────────────┐
│              导航栏                  │
├─────────────────────────────────────┤
│           历史答案                   │
│                                     │
│  #455 | 2025-08-01 | Words that...  │
│  #454 | 2025-07-31 | Types of...    │
│  #453 | 2025-07-30 | Things you...  │
│  ...                                │
│                                     │
│     [上一页]  1 2 3  [下一页]       │
└─────────────────────────────────────┘
```

## 5. 开发计划

### 5.1 第1周
- [ ] 项目初始化和环境配置
- [ ] 数据库表设计和创建
- [ ] 首页基础布局
- [ ] 今日答案API开发
- [ ] 今日答案卡片组件

### 5.2 第2周
- [ ] 历史答案页面开发
- [ ] 历史答案API开发
- [ ] 动态路由实现 ([slug].tsx)
- [ ] 倒计时器组件开发
- [ ] 分页组件开发

### 5.3 第3周
- [ ] 响应式设计优化
- [ ] SEO优化 (meta标签、sitemap)
- [ ] 错误处理和加载状态
- [ ] 性能优化
- [ ] 部署和域名配置

## 6. 数据管理

### 6.1 数据录入
- **手动录入**: 管理员每日手动添加新答案
- **数据格式**: 
  ```json
  {
    "gameNumber": 455,
    "date": "2025-08-01",
    "answer": "Words that come after 'head'",
    "clues": ["book", "point", "list", "mate", "please"]
  }
  ```

### 6.2 URL生成规则
- 格式: `linkedin-pinpoint-{gameNumber}-{clue1}-{clue2}-{clue3}-{clue4}-{clue5}`
- 示例: `linkedin-pinpoint-455-book-point-list-mate-please`
- 特殊字符处理: 转换为小写，空格替换为连字符

### 6.3 更新时间
- 每日太平洋时间午夜后更新
- 倒计时器自动重置

## 7. SEO优化

### 7.1 页面标题和描述
- 首页: "LinkedIn Pinpoint 每日答案 - 今日答案 #{gameNumber}"
- 历史页: "LinkedIn Pinpoint 历史答案 - 所有答案归档"
- 详情页: "Pinpoint #{gameNumber} 答案: {answer}"

### 7.2 关键词优化
- LinkedIn Pinpoint 答案
- Pinpoint 每日答案
- LinkedIn 游戏答案
- Pinpoint 历史答案

## 8. 成功指标

### 8.1 MVP成功标准
- [ ] 网站正常运行，无重大bug
- [ ] 每日答案及时更新 (延迟<2小时)
- [ ] 页面加载速度 < 3秒
- [ ] 移动端正常显示
- [ ] SEO收录正常

### 8.2 用户指标目标
- 日访问量: 100+ (第一个月)
- 页面停留时间: > 1分钟
- 跳出率: < 70%

## 9. 后续迭代计划

### 9.1 V1.1 功能
- 搜索功能
- 答案解释
- 用户收藏

### 9.2 V1.2 功能
- 用户系统
- 评论功能
- 统计分析

---

**文档版本**: MVP v1.0  
**创建日期**: 2025-08-01  
**预计完成**: 2025-08-22 (3周)  
**负责人**: 开发团队
