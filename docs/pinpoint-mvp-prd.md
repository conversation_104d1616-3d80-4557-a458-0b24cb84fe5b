# LinkedIn Pinpoint 每日答案网站 - MVP版本 PRD

## 1. 产品概述

### 1.1 MVP目标
创建一个简洁的LinkedIn Pinpoint每日答案查询网站，专注于核心功能：
- 展示每日答案
- 提供历史答案查询
- 简洁的用户体验

### 1.2 目标用户
- LinkedIn Pinpoint 游戏玩家
- 寻找每日答案的用户

### 1.3 核心价值
- 快速获取准确答案
- 简单易用的界面
- 及时的内容更新

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 首页 (/) - 导航概览页面
**功能描述**: 作为导航和概览页面，不直接展示完整答案
**详细需求**:
- 今日答案预览卡片
  - 游戏编号 (如 #455)
  - 发布日期
  - 答案预览（不显示完整内容）
  - 点击查看完整答案的链接
- 倒计时器
  - 显示距离下一个谜题发布的剩余时间
  - 太平洋时间午夜重置
- 最近答案列表
  - 显示最近5-10个答案的链接
  - 游戏编号、日期、答案类别预览
- 快速导航
  - 历史答案页面链接
  - 简洁的导航菜单

#### 2.1.2 答案详情页 (/linkedin-pinpoint-[number]-[clues]) - 静态生成
**功能描述**: 单个答案的详细页面，使用静态生成优化SEO
**详细需求**:
- URL格式: `/linkedin-pinpoint-455-book-point-list-mate-please`
  - 455: 游戏编号
  - book-point-list-mate-please: 5个线索词用连字符连接
- 页面内容:
  - 游戏编号和日期
  - 5个线索词汇完整展示
  - 正确答案/类别
  - 返回首页/历史页面链接
- SEO优化:
  - 静态HTML生成 (Next.js ISR)
  - 完整的meta标签
  - 结构化数据标记
  - 优化的页面标题和描述

#### 2.1.3 历史答案页 (/history) - 静态生成
**功能描述**: 查看历史答案列表，静态生成优化SEO
**详细需求**:
- 答案列表展示
  - 按日期倒序排列
  - 显示游戏编号、日期、答案预览
  - 每个答案链接到详情页面
- 简单分页
  - 每页显示20条记录
  - 上一页/下一页导航
- SEO优化:
  - 静态生成页面
  - 优化的页面标题和meta描述

## 3. 技术架构

### 3.1 技术栈
- **前端**: Next.js 14+ (已有) + ISR静态生成
- **样式**: Tailwind CSS (已有)
- **UI组件**: Radix UI (已有)
- **数据库**: 基于现有数据库系统 (Supabase)
- **SEO优化**: 静态生成 + 结构化数据
- **部署**: Vercel (推荐)

### 3.2 数据库设计

```sql
-- 简化的答案表
CREATE TABLE pinpoint_daily_answers (
  id BIGINT PRIMARY KEY,
  game_number INTEGER UNIQUE NOT NULL,
  date DATE UNIQUE NOT NULL,
  answer VARCHAR(255) NOT NULL,
  clue_word_1 VARCHAR(100) NOT NULL,
  clue_word_2 VARCHAR(100) NOT NULL,
  clue_word_3 VARCHAR(100) NOT NULL,
  clue_word_4 VARCHAR(100) NOT NULL,
  clue_word_5 VARCHAR(100) NOT NULL,
  url_slug VARCHAR(500) NOT NULL, -- linkedin-pinpoint-455-book-point-list-mate-please
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_game_number ON pinpoint_daily_answers(game_number);
CREATE INDEX idx_date ON pinpoint_daily_answers(date);
CREATE INDEX idx_url_slug ON pinpoint_daily_answers(url_slug);
```

### 3.3 静态生成策略

#### 3.3.1 ISR配置
```typescript
// 答案详情页面
export const revalidate = 3600; // 1小时重新生成

// 首页
export const revalidate = 1800; // 30分钟重新生成

// 历史页面
export const revalidate = 3600; // 1小时重新生成
```

#### 3.3.2 静态数据获取
```typescript
// 构建时获取所有答案数据
export async function getAllPinpointAnswers() {
  const supabase = getSupabaseClient();
  const { data } = await supabase
    .from('pinpoint_daily_answers')
    .select('*')
    .eq('status', 'published')
    .order('date', { ascending: false });
  return data || [];
}
```

#### 3.3.3 管理后台API (保留)
```
POST /api/admin/pinpoint - 创建新答案并触发重新生成
GET /api/admin/pinpoint - 获取答案列表
PUT /api/admin/pinpoint/[id] - 更新答案
DELETE /api/admin/pinpoint/[id] - 删除答案
```

## 4. 页面设计

### 4.1 首页布局 (导航概览页面)
```
┌─────────────────────────────────────┐
│              导航栏                  │
├─────────────────────────────────────┤
│                                     │
│         Pinpoint 每日答案            │
│                                     │
│    ┌─────────────────────────┐      │
│    │     今日答案预览         │      │
│    │  Pinpoint #455          │      │
│    │  2025-08-01             │      │
│    │                         │      │
│    │  点击查看完整答案 →      │      │
│    └─────────────────────────┘      │
│                                     │
│         倒计时器                     │
│    下一题发布: 23:45:12             │
│                                     │
│         最近答案                     │
│    • #454 - Words that start...    │
│    • #453 - Types of music...      │
│    • #452 - Kitchen utensils...    │
│                                     │
│    [查看所有历史答案]               │
│                                     │
└─────────────────────────────────────┘
```

### 4.2 历史答案页布局
```
┌─────────────────────────────────────┐
│              导航栏                  │
├─────────────────────────────────────┤
│           历史答案                   │
│                                     │
│  #455 | 2025-08-01 | Words that...  │
│  #454 | 2025-07-31 | Types of...    │
│  #453 | 2025-07-30 | Things you...  │
│  ...                                │
│                                     │
│     [上一页]  1 2 3  [下一页]       │
└─────────────────────────────────────┘
```

## 5. 开发计划

### 5.1 第1周
- [ ] 项目初始化和环境配置
- [ ] 数据库表设计和创建
- [ ] 首页基础布局
- [ ] 今日答案API开发
- [ ] 今日答案卡片组件

### 5.2 第2周
- [ ] 历史答案页面开发
- [ ] 历史答案API开发
- [ ] 动态路由实现 ([slug].tsx)
- [ ] 倒计时器组件开发
- [ ] 分页组件开发

### 5.3 第3周
- [ ] 响应式设计优化
- [ ] SEO优化 (meta标签、sitemap)
- [ ] 错误处理和加载状态
- [ ] 性能优化
- [ ] 部署和域名配置

## 6. 数据管理

### 6.1 数据录入
- **手动录入**: 管理员每日手动添加新答案
- **数据格式**: 
  ```json
  {
    "gameNumber": 455,
    "date": "2025-08-01",
    "answer": "Words that come after 'head'",
    "clues": ["book", "point", "list", "mate", "please"]
  }
  ```

### 6.2 URL生成规则
- 格式: `linkedin-pinpoint-{gameNumber}-{clue1}-{clue2}-{clue3}-{clue4}-{clue5}`
- 示例: `linkedin-pinpoint-455-book-point-list-mate-please`
- 特殊字符处理: 转换为小写，空格替换为连字符

### 6.3 更新时间
- 每日太平洋时间午夜后更新
- 倒计时器自动重置

## 7. SEO优化策略

### 7.1 静态生成优化
- **ISR配置**: 所有页面使用增量静态再生成
- **构建时生成**: 答案页面在构建时预生成
- **自动重新生成**: 管理后台更新后触发页面重新生成

### 7.2 页面标题和描述优化
- 首页: "LinkedIn Pinpoint 每日答案 - 最新游戏答案和历史记录"
- 历史页: "LinkedIn Pinpoint 历史答案 - 完整答案归档和搜索"
- 详情页: "Pinpoint #{gameNumber} 答案: {answer} | LinkedIn Pinpoint 每日答案"

### 7.3 关键词和内容优化
- **主要关键词**: LinkedIn Pinpoint 答案、Pinpoint 每日答案、LinkedIn 游戏答案
- **长尾关键词**: 包含具体游戏编号和答案内容
- **结构化数据**: 添加JSON-LD结构化数据标记
- **内部链接**: 首页链接到所有答案页面，提升页面权重

### 7.4 技术SEO
- **URL结构**: 统一使用 `/linkedin-pinpoint-[number]-[clues]` 格式
- **Sitemap**: 自动生成包含所有答案页面的sitemap
- **Meta标签**: 每个页面独特的title、description、keywords
- **加载速度**: 静态HTML确保极快的加载速度

## 8. 成功指标

### 8.1 MVP成功标准
- [ ] 网站正常运行，无重大bug
- [ ] 每日答案及时更新 (延迟<2小时)
- [ ] 页面加载速度 < 3秒
- [ ] 移动端正常显示
- [ ] SEO收录正常

### 8.2 用户指标目标
- 日访问量: 100+ (第一个月)
- 页面停留时间: > 1分钟
- 跳出率: < 70%

## 9. 后续迭代计划

### 9.1 V1.1 功能
- 搜索功能
- 答案解释
- 用户收藏

### 9.2 V1.2 功能
- 用户系统
- 评论功能
- 统计分析

---

**文档版本**: MVP v1.0  
**创建日期**: 2025-08-01  
**预计完成**: 2025-08-22 (3周)  
**负责人**: 开发团队
