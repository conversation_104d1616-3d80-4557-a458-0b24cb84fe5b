#!/bin/bash

# LinkedIn Pinpoint Crawler - Cloudflare Workers 部署脚本

echo "🚀 开始部署 Pinpoint Crawler 到 Cloudflare Workers..."

# 检查是否安装了 wrangler
if ! command -v wrangler &> /dev/null; then
    echo "❌ wrangler 未安装，正在安装..."
    npm install -g wrangler
fi

# 检查是否已登录 Cloudflare
echo "🔐 检查 Cloudflare 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "请先登录 Cloudflare:"
    wrangler login
fi

# 设置环境变量（敏感信息）
echo "🔑 设置环境变量..."
echo "请输入 Supabase Service Role Key:"
wrangler secret put SUPABASE_SERVICE_ROLE_KEY

echo "请输入网站重新生成密钥（可选，如果网站需要认证）:"
read -p "是否需要设置 REVALIDATE_SECRET? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    wrangler secret put REVALIDATE_SECRET
fi

# 部署到开发环境
echo "📦 部署到开发环境..."
wrangler deploy --env development

# 询问是否部署到生产环境
read -p "是否部署到生产环境? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌟 部署到生产环境..."
    wrangler deploy --env production
fi

echo "✅ 部署完成！"
echo ""
echo "📋 部署信息:"
echo "- 开发环境: pinpoint-crawler-dev (每5分钟执行一次)"
echo "- 生产环境: pinpoint-crawler-prod (每小时执行一次)"
echo ""
echo "🔧 管理命令:"
echo "- 查看日志: wrangler tail"
echo "- 手动触发: curl https://pinpoint-crawler-prod.你的用户名.workers.dev/manual"
echo "- 测试定时任务: wrangler dev --test-scheduled"
echo ""
echo "⚠️  注意事项:"
echo "1. 请在 wrangler.toml 中更新 WEBSITE_URL 为你的实际域名"
echo "2. 确保 Supabase 服务密钥有正确的权限"
echo "3. 监控 Worker 的执行日志以确保正常运行"
