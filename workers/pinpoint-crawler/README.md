# LinkedIn Pinpoint Crawler - Cloudflare Workers

自动抓取LinkedIn Pinpoint每日答案并更新到Supabase数据库的Cloudflare Worker。

## 🚀 功能特性

- ✅ **自动定时抓取** - 每小时检查一次新答案
- ✅ **智能去重** - 自动检测已存在的答案，避免重复
- ✅ **数据验证** - 确保抓取数据的完整性
- ✅ **自动更新** - 直接更新到Supabase数据库
- ✅ **页面刷新** - 自动触发网站页面重新生成
- ✅ **错误处理** - 完善的错误处理和日志记录
- ✅ **手动触发** - 支持手动触发抓取

## 📋 部署步骤

### 1. 安装依赖

```bash
cd workers/pinpoint-crawler
npm install
```

### 2. 配置环境

编辑 `wrangler.toml` 文件，更新以下配置：

```toml
[vars]
WEBSITE_URL = "https://你的实际域名.com"
```

### 3. 设置敏感信息

```bash
# 设置 Supabase 服务密钥
wrangler secret put SUPABASE_SERVICE_ROLE_KEY

# 设置网站重新生成密钥（可选）
wrangler secret put REVALIDATE_SECRET
```

### 4. 部署

```bash
# 使用部署脚本（推荐）
chmod +x deploy.sh
./deploy.sh

# 或手动部署
wrangler deploy --env development  # 开发环境
wrangler deploy --env production   # 生产环境
```

## ⏰ 定时任务配置

- **开发环境**: 每5分钟执行一次 (`*/5 * * * *`)
- **生产环境**: 每小时执行一次 (`0 * * * *`)

## 🔧 管理命令

```bash
# 查看实时日志
wrangler tail

# 手动触发抓取
curl https://pinpoint-crawler-prod.你的用户名.workers.dev/manual

# 本地开发测试
wrangler dev

# 测试定时任务
wrangler dev --test-scheduled
```

## 📊 监控和调试

### 查看执行日志

```bash
wrangler tail --env production
```

### 手动测试

访问 Worker URL 的 `/manual` 端点来手动触发抓取：

```
https://pinpoint-crawler-prod.你的用户名.workers.dev/manual
```

### 响应格式

成功响应：
```json
{
  "success": true,
  "message": "数据抓取和插入成功",
  "data": {
    "game_number": 458,
    "date": "2025-08-01",
    "answer": "Words that come after 'head'",
    "clues": ["book", "point", "list", "mate", "please"],
    "fetched_at": "2025-08-01T12:00:00.000Z"
  }
}
```

失败响应：
```json
{
  "success": false,
  "message": "数据不完整，缺失字段: game_number",
  "data": {...}
}
```

## 🔒 安全配置

### 环境变量

- `SUPABASE_SERVICE_ROLE_KEY`: Supabase服务角色密钥
- `REVALIDATE_SECRET`: 网站重新生成API的认证密钥（可选）
- `WEBSITE_URL`: 你的网站URL

### 权限要求

确保Supabase服务密钥具有以下权限：
- `pinpoint_daily_answers` 表的读写权限
- 能够执行 SELECT、INSERT、UPDATE 操作

## 🐛 故障排除

### 常见问题

1. **抓取失败**
   - 检查目标网站是否可访问
   - 确认HTML结构是否发生变化

2. **数据库操作失败**
   - 验证Supabase密钥是否正确
   - 检查数据库表结构是否匹配

3. **定时任务不执行**
   - 确认Cron表达式格式正确
   - 检查Worker是否成功部署

### 调试技巧

```bash
# 查看详细日志
wrangler tail --format pretty

# 本地测试
wrangler dev --local

# 检查Worker状态
wrangler status
```

## 📈 性能优化

- Worker执行时间通常在1-3秒内
- 每次执行消耗约10-50KB流量
- 建议监控执行频率，避免过度请求

## 🔄 更新和维护

### 更新Worker代码

```bash
# 修改代码后重新部署
wrangler deploy --env production
```

### 修改定时任务

编辑 `wrangler.toml` 中的 `crons` 配置，然后重新部署。

## 📞 支持

如果遇到问题，请检查：
1. Cloudflare Workers 控制台的执行日志
2. Supabase 数据库的连接状态
3. 目标网站的可访问性

---

**注意**: 请遵守目标网站的robots.txt和使用条款，合理控制抓取频率。
