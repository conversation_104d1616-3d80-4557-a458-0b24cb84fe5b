// Cloudflare Worker for LinkedIn Pinpoint Crawler
// 每小时自动抓取并更新Pinpoint答案

const PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/";

// Supabase配置 - 从环境变量获取
const SUPABASE_URL = "https://urjvwlqymdokurivceni.supabase.co";

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    if (url.pathname === '/manual') {
      // 手动触发接口
      return await handleCrawl(env);
    }
    
    return new Response('Pinpoint Crawler Worker is running!', { status: 200 });
  },

  async scheduled(event, env, ctx) {
    // 定时任务处理
    console.log('Scheduled crawl triggered at:', new Date().toISOString());
    ctx.waitUntil(handleCrawl(env));
  }
};

async function handleCrawl(env) {
  try {
    console.log('🔍 开始获取 Pinpoint 游戏数据...');
    
    // 获取网页内容
    const response = await fetch(PINPOINT_URL, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const html = await response.text();
    const data = parsePinpointPage(html);
    data.fetched_at = new Date().toISOString();
    
    console.log('📊 获取到的数据:', JSON.stringify(data, null, 2));
    
    // 检查数据完整性
    if (data.game_number && data.date && data.answer && data.clues && data.clues.length > 0) {
      console.log('💾 正在插入数据到 Supabase...');
      const success = await insertToSupabase(data, env);
      
      if (success) {
        console.log('🎉 任务完成！');
        return new Response(JSON.stringify({
          success: true,
          message: '数据抓取和插入成功',
          data: data
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        throw new Error('数据插入失败');
      }
    } else {
      const missingFields = [];
      if (!data.game_number) missingFields.push('game_number');
      if (!data.date) missingFields.push('date');
      if (!data.answer) missingFields.push('answer');
      if (!data.clues || data.clues.length === 0) missingFields.push('clues');
      
      const errorMsg = `数据不完整，缺失字段: ${missingFields.join(', ')}`;
      console.log('⚠️', errorMsg);
      
      return new Response(JSON.stringify({
        success: false,
        message: errorMsg,
        data: data
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
  } catch (error) {
    console.error('❌ 爬虫执行失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error.message,
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

function parsePinpointPage(html) {
  // 简单的HTML解析，不使用BeautifulSoup
  let gameNumber = null;
  let dateStr = null;
  let answer = null;
  let clues = [];
  
  // 提取日期 - 匹配 "Today's Pinpoint Answer (2025-08-01)" 格式
  const dateMatch = html.match(/Today.s Pinpoint Answer\s*\((\d{4}-\d{1,2}-\d{1,2})\)/);
  if (dateMatch) {
    dateStr = dateMatch[1];
  }
  
  // 提取游戏编号 - 匹配 "The pinpoint game number is 458" 格式
  const gameMatch = html.match(/pinpoint game number is (\d+)/i);
  if (gameMatch) {
    gameNumber = parseInt(gameMatch[1]);
  }
  
  // 提取答案 - 匹配 "Pinpoint Today Answer :" 后面的内容
  const answerMatch = html.match(/Pinpoint Today Answer\s*:\s*([^\n\r]+)/);
  if (answerMatch) {
    answer = answerMatch[1].trim();
  }
  
  // 提取线索词 - 匹配 "Pinpoint Clues :" 后面的内容
  const cluesMatch = html.match(/Pinpoint Clues\s*:\s*([\s\S]*?)(?=\n\s*\n|\n\s*[A-Z]|$)/);
  if (cluesMatch) {
    const cluesText = cluesMatch[1];
    // 分割行并清理
    const lines = cluesText.split(/\n/).map(line => line.trim()).filter(line => line.length > 0);
    clues = lines.slice(0, 5); // 只取前5个
  }
  
  return {
    game_number: gameNumber,
    date: dateStr,
    answer: answer,
    clues: clues
  };
}

function generateUrlSlug(gameNumber, clues) {
  if (!gameNumber || !clues || clues.length === 0) {
    return "";
  }
  
  // 将线索词转换为小写并连接
  const clueParts = clues.slice(0, 5).map(clue => 
    clue.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .replace(/-+/g, '-') // 多个连字符合并为一个
      .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
  ).filter(part => part.length > 0);
  
  const slugParts = ["linkedin", "pinpoint", gameNumber.toString(), ...clueParts];
  return slugParts.join("-");
}

async function insertToSupabase(data, env) {
  try {
    // 准备数据
    const clues = data.clues || [];
    // 确保有5个线索词，不足的用空字符串填充
    while (clues.length < 5) {
      clues.push("");
    }
    
    const urlSlug = generateUrlSlug(data.game_number, clues);
    const gameNumber = data.game_number;
    
    // 检查是否已存在
    const checkResponse = await fetch(`${SUPABASE_URL}/rest/v1/pinpoint_daily_answers?game_number=eq.${gameNumber}&select=id`, {
      headers: {
        'apikey': env.SUPABASE_SERVICE_ROLE_KEY,
        'Authorization': `Bearer ${env.SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!checkResponse.ok) {
      throw new Error(`Supabase check failed: ${checkResponse.status}`);
    }
    
    const existing = await checkResponse.json();
    
    const insertData = {
      game_number: gameNumber,
      date: data.date,
      answer: data.answer,
      clue_word_1: clues[0] || "",
      clue_word_2: clues[1] || "",
      clue_word_3: clues[2] || "",
      clue_word_4: clues[3] || "",
      clue_word_5: clues[4] || "",
      url_slug: urlSlug,
      status: "published"
    };
    
    let supabaseResponse;
    
    if (existing && existing.length > 0) {
      // 记录已存在，执行更新
      supabaseResponse = await fetch(`${SUPABASE_URL}/rest/v1/pinpoint_daily_answers?game_number=eq.${gameNumber}`, {
        method: 'PATCH',
        headers: {
          'apikey': env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify(insertData)
      });
      console.log(`🔄 游戏编号 ${gameNumber} 已存在，数据已更新`);
    } else {
      // 记录不存在，执行插入
      supabaseResponse = await fetch(`${SUPABASE_URL}/rest/v1/pinpoint_daily_answers`, {
        method: 'POST',
        headers: {
          'apikey': env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify(insertData)
      });
      console.log(`✅ 新数据已成功插入到 Supabase，游戏编号: ${gameNumber}`);
    }
    
    if (!supabaseResponse.ok) {
      const errorText = await supabaseResponse.text();
      throw new Error(`Supabase operation failed: ${supabaseResponse.status} - ${errorText}`);
    }
    
    // 触发网站页面重新生成
    await triggerRevalidation(env, urlSlug);
    
    return true;
    
  } catch (error) {
    console.error('❌ 操作 Supabase 失败:', error);
    return false;
  }
}

async function triggerRevalidation(env, urlSlug) {
  try {
    // 调用你的网站API来触发页面重新生成
    const revalidateUrl = `${env.WEBSITE_URL}/api/revalidate`;
    
    const response = await fetch(revalidateUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${env.REVALIDATE_SECRET}` // 如果需要认证
      },
      body: JSON.stringify({
        paths: ['/', '/history', `/${urlSlug}`]
      })
    });
    
    if (response.ok) {
      console.log('✅ 页面重新生成触发成功');
    } else {
      console.log('⚠️ 页面重新生成触发失败，但数据已保存');
    }
  } catch (error) {
    console.log('⚠️ 页面重新生成触发失败:', error.message);
  }
}
