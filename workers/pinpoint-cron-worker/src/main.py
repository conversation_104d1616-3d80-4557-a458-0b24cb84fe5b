from workers import handler
import json
import re
from urllib.parse import urlencode
from urllib.request import urlopen, Request

# 配置
PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/"
API_ENDPOINT = "https://pinpointodays.qinggeng6.workers.dev/api/crawler/pinpoint"
CRAWLER_SECRET = "pinpoint-crawler-secret-2025"

@handler
async def on_scheduled(controller, env, ctx):
    """
    Cron触发器处理函数 - 每小时执行一次
    """
    print("🕐 Cron任务开始执行 - Pinpoint爬虫")
    
    try:
        # 调用主站的爬虫API
        headers = {
            'Authorization': f'Bearer {CRAWLER_SECRET}',
            'Content-Type': 'application/json',
            'User-Agent': 'Cloudflare-Workers-Cron/1.0'
        }
        
        # 创建请求
        req = Request(API_ENDPOINT, method='POST', headers=headers)
        
        # 发送请求
        with urlopen(req) as response:
            result = json.loads(response.read().decode())
            
        if response.status == 200:
            print(f"✅ 爬虫执行成功: {result.get('message', 'Success')}")
            
            # 记录执行结果
            data = result.get('data', {})
            if data:
                print(f"📊 数据摘要:")
                print(f"   - 游戏编号: {data.get('game_number')}")
                print(f"   - 日期: {data.get('date')}")
                print(f"   - 答案: {data.get('answer')}")
                print(f"   - 线索数量: {len(data.get('clues', []))}")
                
        else:
            print(f"❌ 爬虫执行失败: HTTP {response.status}")
            print(f"   响应: {result}")
            
    except Exception as error:
        print(f"❌ Cron任务执行异常: {str(error)}")
        # 不抛出异常，避免影响后续执行
        
    print("🏁 Cron任务执行完成")

@handler  
async def on_fetch(request, env, ctx):
    """
    HTTP请求处理函数 - 用于健康检查和手动触发
    """
    url = request.url
    
    if url.endswith('/health'):
        return Response(json.dumps({
            "status": "healthy",
            "service": "Pinpoint Cron Worker",
            "timestamp": controller.scheduled_time if hasattr(controller, 'scheduled_time') else None
        }), headers={'Content-Type': 'application/json'})
    
    elif url.endswith('/trigger'):
        # 手动触发爬虫
        print("🔧 手动触发爬虫任务")
        
        # 模拟scheduled事件
        class MockController:
            def __init__(self):
                import time
                self.scheduled_time = int(time.time() * 1000)
                
        await on_scheduled(MockController(), env, ctx)
        
        return Response(json.dumps({
            "message": "爬虫任务已手动触发",
            "timestamp": MockController().scheduled_time
        }), headers={'Content-Type': 'application/json'})
    
    else:
        return Response(json.dumps({
            "message": "Pinpoint Cron Worker",
            "endpoints": {
                "/health": "健康检查",
                "/trigger": "手动触发爬虫"
            }
        }), headers={'Content-Type': 'application/json'})

# 导出默认处理器
export = {
    "scheduled": on_scheduled,
    "fetch": on_fetch
}
