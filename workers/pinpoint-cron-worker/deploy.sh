#!/bin/bash

# Pinpoint Cron Worker 部署脚本

echo "🚀 开始部署 Pinpoint Cron Worker..."

# 检查wrangler是否安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler 未安装，请先安装："
    echo "npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "🔐 请先登录 Cloudflare："
    wrangler login
fi

# 进入Worker目录
cd "$(dirname "$0")"

echo "📦 部署 Python Worker..."
wrangler deploy

if [ $? -eq 0 ]; then
    echo "✅ 部署成功！"
    echo ""
    echo "🔗 Worker URL: https://pinpoint-cron-worker.你的用户名.workers.dev"
    echo ""
    echo "📋 可用端点："
    echo "  - /health     - 健康检查"
    echo "  - /trigger    - 手动触发爬虫"
    echo ""
    echo "⏰ Cron任务已配置为每小时执行一次"
    echo ""
    echo "🔍 查看日志："
    echo "  wrangler tail pinpoint-cron-worker"
    echo ""
    echo "🧪 测试手动触发："
    echo "  curl https://pinpoint-cron-worker.你的用户名.workers.dev/trigger"
else
    echo "❌ 部署失败"
    exit 1
fi
