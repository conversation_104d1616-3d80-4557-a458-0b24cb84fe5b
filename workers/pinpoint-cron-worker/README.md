# Pinpoint Cron Worker

基于 Cloudflare Workers 的 Python 定时任务，用于自动抓取 Pinpoint 游戏答案。

## 🎯 功能特性

- ✅ **自动调度** - 每小时自动执行一次
- ✅ **Python 支持** - 使用 Cloudflare Workers Python 运行时
- ✅ **健康检查** - 提供健康检查端点
- ✅ **手动触发** - 支持手动触发爬虫任务
- ✅ **日志记录** - 详细的执行日志
- ✅ **错误处理** - 优雅的错误处理机制

## 🚀 部署步骤

### 1. 安装依赖
```bash
# 安装 Wrangler CLI
npm install -g wrangler

# 登录 Cloudflare
wrangler login
```

### 2. 配置环境变量
编辑 `wrangler.toml` 文件，更新以下配置：
- `API_ENDPOINT` - 你的主站爬虫API地址
- `CRAWLER_SECRET` - 爬虫API密钥

### 3. 部署Worker
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

## 📋 API端点

### GET /health
健康检查端点
```bash
curl https://pinpoint-cron-worker.你的用户名.workers.dev/health
```

### GET /trigger  
手动触发爬虫任务
```bash
curl https://pinpoint-cron-worker.你的用户名.workers.dev/trigger
```

## ⏰ Cron调度

Worker配置为每小时的第0分钟执行：
- Cron表达式: `0 * * * *`
- 执行时间: 每小时整点 (UTC时间)

## 🔍 监控和调试

### 查看实时日志
```bash
wrangler tail pinpoint-cron-worker
```

### 查看执行历史
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 Workers & Pages
3. 选择 `pinpoint-cron-worker`
4. 查看 Settings > Trigger Events

## 🛠️ 工作原理

1. **Cron触发** - Cloudflare每小时触发scheduled事件
2. **调用API** - Worker调用主站的爬虫API
3. **数据处理** - 主站API负责数据抓取和存储
4. **日志记录** - Worker记录执行结果和状态

## 🔧 配置说明

### wrangler.toml 配置项
- `name` - Worker名称
- `main` - Python入口文件
- `triggers.crons` - Cron表达式数组
- `vars` - 环境变量配置

### 环境变量
- `PINPOINT_URL` - 目标网站URL
- `API_ENDPOINT` - 主站爬虫API地址  
- `CRAWLER_SECRET` - API认证密钥

## 📊 优势对比

| 特性 | Python Worker | Next.js集成 |
|------|---------------|-------------|
| **部署复杂度** | 独立部署 | 集成部署 |
| **语言支持** | ✅ 原生Python | JavaScript/TypeScript |
| **调度稳定性** | ✅ 专用Cron | 依赖框架支持 |
| **资源隔离** | ✅ 完全隔离 | 共享资源 |
| **维护成本** | 独立维护 | 统一维护 |

## 🚨 注意事项

1. **Python限制** - Cloudflare Workers Python运行时有一些限制
2. **执行时间** - 单次执行不能超过30秒
3. **内存限制** - 128MB内存限制
4. **网络请求** - 支持HTTP/HTTPS请求
5. **时区** - Cron执行基于UTC时间

## 🔄 更新和维护

### 更新代码
```bash
# 修改代码后重新部署
wrangler deploy
```

### 更新Cron时间
编辑 `wrangler.toml` 中的 `triggers.crons` 配置，然后重新部署。

### 删除Worker
```bash
wrangler delete pinpoint-cron-worker
```
