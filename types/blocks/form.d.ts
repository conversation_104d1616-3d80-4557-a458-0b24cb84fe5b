import { Button } from "@/types/blocks/base";

type ValidationRule = {
  required?: boolean;
  min?: number;
  max?: number;
  maxLength?: number;
  message?: string;
  email?: boolean;
};

export interface FormField {
  name?: string;
  title?: string;
  type?:
    | "text"
    | "textarea"
    | "number"
    | "email"
    | "password"
    | "select"
    | "url"
    | "date"
    | "editor"
    | "code_editor"
    | "richtext_editor"
    | "markdown_editor";
  placeholder?: string;
  options?: {
    title: string;
    value: string;
  }[];
  value?: string;
  default_value?: string;
  tip?: string;
  attributes?: Record<string, any>;
  validation?: ValidationRule;
}

export interface FormSubmit {
  button?: Button;
  handler?: (
    data: FormData,
    passby?: any
  ) => Promise<
    | {
        status: "success" | "error";
        message: string;
        redirect_url?: string;
      }
    | undefined
    | void
  >;
  action?: (formData: FormData) => Promise<any>;
}

export interface Form {
  fields: FormField[];
  data?: any;
  submit?: FormSubmit;
}
