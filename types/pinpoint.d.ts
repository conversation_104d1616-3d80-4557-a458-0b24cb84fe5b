// LinkedIn Pinpoint 相关类型定义

export interface PinpointAnswer {
  id?: number;
  game_number: number;
  date: string; // YYYY-MM-DD 格式
  answer: string;
  clue_word_1: string;
  clue_word_2: string;
  clue_word_3: string;
  clue_word_4: string;
  clue_word_5: string;
  url_slug: string;
  created_at?: string;
  updated_at?: string;
  status?: 'published' | 'draft' | 'deleted';
}

// 前端展示用的格式化数据
export interface PinpointAnswerFormatted {
  gameNumber: number;
  date: string;
  answer: string;
  clues: [string, string, string, string, string];
  urlSlug: string;
  createdAt?: string;
  updatedAt?: string;
  status?: 'published' | 'draft' | 'deleted';
}

// 管理后台表单数据
export interface PinpointFormData {
  gameNumber: number;
  date: string;
  answer: string;
  clueWords: [string, string, string, string, string];
  status?: 'published' | 'draft' | 'deleted';
}

// API响应格式
export interface PinpointApiResponse {
  success: boolean;
  data?: PinpointAnswerFormatted;
  error?: string;
}

// 历史答案列表响应
export interface PinpointListResponse {
  success: boolean;
  data?: PinpointAnswerFormatted[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

// 管理后台列表响应
export interface PinpointAdminListResponse {
  success: boolean;
  data?: PinpointAnswer[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}

// 静态生成参数
export interface PinpointStaticParams {
  slug: string;
}

// 页面元数据生成参数
export interface PinpointMetadataParams {
  params: PinpointStaticParams;
}

// 倒计时器数据
export interface CountdownData {
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
  isExpired: boolean;
}

// 最近答案预览数据
export interface RecentAnswerPreview {
  gameNumber: number;
  date: string;
  answer: string;
  urlSlug: string;
}

// 今日答案预览数据
export interface TodayAnswerPreview {
  gameNumber: number;
  date: string;
  hasAnswer: boolean;
  urlSlug?: string;
  answerPreview?: string; // 答案的简短预览，不是完整答案
}

// 搜索/筛选参数
export interface PinpointSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'published' | 'draft' | 'deleted' | 'all';
  dateFrom?: string;
  dateTo?: string;
  gameNumberFrom?: number;
  gameNumberTo?: number;
}

// 数据验证错误
export interface ValidationError {
  field: string;
  message: string;
}

// 表单验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// 页面重新生成状态
export interface RevalidationStatus {
  success: boolean;
  paths: string[];
  timestamp: string;
  error?: string;
}

// SEO相关类型
export interface PinpointSEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl: string;
  openGraph: {
    title: string;
    description: string;
    type: 'article' | 'website';
    publishedTime?: string;
    url: string;
  };
  twitter: {
    card: 'summary' | 'summary_large_image';
    title: string;
    description: string;
  };
  jsonLd: Record<string, any>;
}

// 组件Props类型
export interface TodayAnswerPreviewProps {
  answer?: TodayAnswerPreview;
  loading?: boolean;
}

export interface CountdownTimerProps {
  targetTime?: Date;
  onExpired?: () => void;
}

export interface RecentAnswersListProps {
  answers: RecentAnswerPreview[];
  loading?: boolean;
  showCount?: number;
}

export interface AnswerDetailProps {
  answer: PinpointAnswerFormatted;
  showNavigation?: boolean;
}

export interface JsonLdProps {
  answer: PinpointAnswerFormatted;
}

// 管理后台组件Props
export interface PinpointFormProps {
  initialData?: Partial<PinpointFormData>;
  onSubmit: (data: PinpointFormData) => Promise<void>;
  loading?: boolean;
  mode: 'create' | 'edit';
}

export interface PinpointListTableProps {
  answers: PinpointAnswer[];
  loading?: boolean;
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onStatusChange: (id: number, status: PinpointAnswer['status']) => void;
}

// 工具函数类型
export type SlugGenerator = (gameNumber: number, clueWords: string[]) => string;
export type DataValidator = (data: any) => ValidationResult;
export type TimeCalculator = () => Date;
export type FormatHelper = (answer: PinpointAnswer) => PinpointAnswerFormatted;

// 常量类型
export const PINPOINT_STATUS = {
  PUBLISHED: 'published',
  DRAFT: 'draft',
  DELETED: 'deleted',
} as const;

export const REVALIDATE_TIMES = {
  ANSWER_PAGE: 3600, // 1小时
  HOME_PAGE: 1800,   // 30分钟
  HISTORY_PAGE: 3600, // 1小时
} as const;

export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// 导出所有状态类型
export type PinpointStatus = typeof PINPOINT_STATUS[keyof typeof PINPOINT_STATUS];
