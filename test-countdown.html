<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒计时文案测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f7;
        }
        .card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .countdown-container {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            padding: 16px 24px;
            border-radius: 16px;
            background: #f6f6f6;
        }
        .countdown-time {
            font-size: 24px;
            font-family: 'SF Mono', Monaco, monospace;
            font-weight: bold;
            color: #007AFF;
        }
        .countdown-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
        }
        .highlight {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 6px;
            background: #007AFF;
            color: white;
            display: inline-block;
        }
        .clock-icon {
            width: 20px;
            height: 20px;
            color: #007AFF;
        }
        h2 {
            color: #1d1d1f;
            margin-bottom: 16px;
        }
        .demo-section {
            margin: 32px 0;
        }
    </style>
</head>
<body>
    <h1>LinkedIn Pinpoint 倒计时文案测试</h1>
    
    <div class="demo-section">
        <h2>🇺🇸 英文版本</h2>
        <div class="card">
            <div class="countdown-container">
                <svg class="clock-icon" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"/>
                </svg>
                <div>
                    <div class="countdown-label">
                        Next Puzzle 
                        <span class="highlight">LinkedIn Pinpoint #461</span>
                        Countdown
                    </div>
                    <div class="countdown-time">12:34:56</div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>🇨🇳 中文版本</h2>
        <div class="card">
            <div class="countdown-container">
                <svg class="clock-icon" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"/>
                </svg>
                <div>
                    <div class="countdown-label">
                        下一个谜题 
                        <span class="highlight">LinkedIn Pinpoint #461</span>
                        倒计时
                    </div>
                    <div class="countdown-time">12:34:56</div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>📱 移动端效果</h2>
        <div class="card" style="max-width: 320px;">
            <div class="countdown-container" style="flex-direction: column; text-align: center;">
                <svg class="clock-icon" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"/>
                </svg>
                <div class="countdown-label">
                    Next Puzzle 
                    <span class="highlight">LinkedIn Pinpoint #461</span>
                    Countdown
                </div>
                <div class="countdown-time">12:34:56</div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>🎨 不同游戏编号示例</h2>
        <div class="card">
            <p><strong>当前游戏编号 #460，下一个应该是 #461</strong></p>
            <p><strong>当前游戏编号 #500，下一个应该是 #501</strong></p>
            <p><strong>当前游戏编号 #999，下一个应该是 #1000</strong></p>
            
            <div style="margin-top: 16px;">
                <div class="countdown-label" style="margin-bottom: 8px;">
                    Next Puzzle 
                    <span class="highlight">LinkedIn Pinpoint #501</span>
                    Countdown
                </div>
                <div class="countdown-label">
                    Next Puzzle 
                    <span class="highlight">LinkedIn Pinpoint #1000</span>
                    Countdown
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟倒计时更新
        function updateCountdown() {
            const now = new Date();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            const timeElements = document.querySelectorAll('.countdown-time');
            timeElements.forEach(el => {
                el.textContent = `${hours}:${minutes}:${seconds}`;
            });
        }

        // 每秒更新一次
        setInterval(updateCountdown, 1000);
        updateCountdown();
    </script>
</body>
</html>
