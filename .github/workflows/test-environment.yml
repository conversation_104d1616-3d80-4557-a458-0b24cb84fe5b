name: Test Environment

on:
  # 仅手动触发，用于测试环境
  workflow_dispatch:

jobs:
  test-environment:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r app/scripts/requirements.txt
        
    - name: Test Environment
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🧪 开始环境测试..."
        python app/scripts/test_github_actions.py
        
    - name: Test DailyCode Crawler (Dry Run)
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🧪 测试 DailyCode 爬虫（仅获取数据，不插入数据库）..."
        # 这里可以添加一个 --dry-run 参数的版本
