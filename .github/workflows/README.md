# GitHub Actions Workflows for Pinpoint Crawlers

## 概述

本项目包含多个 GitHub Actions workflows 用于自动化执行 Pinpoint 游戏数据爬取任务。

## Workflows 说明

### 1. `pinpoint-crawler.yml` - 原始 Phonenumble 爬虫
- **目标网站**: phonenumble.com 相关网站
- **执行时间**: 每10分钟执行一次（UTC 7:00-8:00）
- **脚本**: `app/scripts/pinpoint_crawler.py`

### 2. `pinpoint-crawler-dailycode.yml` - DailyCode 专用爬虫
- **目标网站**: https://www.dailycode.com.ng/Pinpoint-hint-answer-today
- **执行时间**: 每天 UTC 8:00 执行一次
- **脚本**: `app/scripts/pinpoint_crawler_dailycode.py`
- **特点**: 
  - 自动处理30秒等待时间
  - 智能编码修复
  - 完整答案获取

### 3. `pinpoint-crawler-all.yml` - 组合爬虫（推荐）
- **功能**: 可以运行所有爬虫或选择性运行
- **执行时间**: 每天 UTC 8:00 执行一次
- **手动触发**: 支持选择运行特定爬虫
- **容错**: 单个爬虫失败不影响其他爬虫

## 环境变量配置

在 GitHub 仓库的 Settings > Secrets and variables > Actions 中配置：

```
SUPABASE_URL=https://urjvwlqymdokurivceni.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 手动触发

### 单独运行 DailyCode 爬虫
1. 进入 GitHub 仓库
2. 点击 Actions 标签
3. 选择 "Pinpoint DailyCode Crawler"
4. 点击 "Run workflow"

### 运行所有爬虫或选择性运行
1. 进入 GitHub 仓库
2. 点击 Actions 标签  
3. 选择 "All Pinpoint Crawlers"
4. 点击 "Run workflow"
5. 选择要运行的爬虫类型：
   - `all`: 运行所有爬虫
   - `phonenumble`: 只运行原始爬虫
   - `dailycode`: 只运行 DailyCode 爬虫

## 执行时间说明

### UTC 时间对应
- **UTC 8:00** = 北京时间 16:00 = 美国东部时间 4:00 AM
- 选择这个时间是因为：
  - 避开网站高峰期
  - 确保当天数据已更新
  - DailyCode 网站有30秒加载时间，早上执行更稳定

### Cron 表达式
```yaml
# 每天 UTC 8:00 执行
- cron: '0 8 * * *'

# 每10分钟执行（UTC 7:00-8:00）
- cron: '*/10 7,8 * * *'
```

## 监控和日志

### 成功执行
- 控制台会显示 ✅ 成功信息
- 数据会自动插入到 Supabase 数据库

### 失败处理
- 失败时会上传日志文件
- 日志保留7天
- 可以在 Actions 页面查看详细错误信息

### 查看执行历史
1. 进入 GitHub 仓库
2. 点击 Actions 标签
3. 选择对应的 workflow
4. 查看执行历史和日志

## 推荐使用方式

### 日常使用
推荐使用 `pinpoint-crawler-all.yml`，因为：
- 可以同时运行多个爬虫
- 单个失败不影响其他爬虫
- 支持选择性运行
- 更好的错误处理

### 测试调试
1. 使用手动触发功能
2. 选择单个爬虫进行测试
3. 查看日志确认问题

### 生产环境
- 设置定时执行
- 监控执行状态
- 定期检查数据库数据

## 故障排除

### 常见问题
1. **环境变量未设置**: 检查 GitHub Secrets 配置
2. **网络超时**: 脚本有重试机制，通常会自动恢复
3. **数据解析失败**: 可能是网站结构变化，需要更新脚本

### 调试步骤
1. 查看 Actions 执行日志
2. 手动触发单个爬虫测试
3. 检查 Supabase 数据库连接
4. 验证网站是否可访问

## 维护建议

1. **定期检查**: 每周检查一次执行状态
2. **更新依赖**: 定期更新 requirements.txt
3. **监控数据**: 确保数据正常插入数据库
4. **备份配置**: 保存 workflow 配置的备份
