name: All Pinpoint Crawlers

on:
  # 定时触发：每天 UTC 8:00 执行
  schedule:
    - cron: '*/10 7,8 * * *'
  
  # 手动触发，用于测试
  workflow_dispatch:
    inputs:
      crawler_type:
        description: '选择要运行的爬虫'
        required: false
        default: 'all'
        type: choice
        options:
        - all
        - phonenumble
        - dailycode

jobs:
  crawl-pinpoint-all:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-all-${{ hashFiles('**/app/scripts/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-all-
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r app/scripts/requirements.txt
        
    - name: Run Phonenumble Pinpoint Crawler
      if: ${{ github.event.inputs.crawler_type == 'all' || github.event.inputs.crawler_type == 'phonenumble' || github.event.inputs.crawler_type == '' }}
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🔍 开始执行 Phonenumble Pinpoint 爬虫任务..."
        python app/scripts/pinpoint_crawler.py || echo "⚠️ Phonenumble 爬虫执行失败，继续执行其他爬虫"
        
    - name: Wait between crawlers
      if: ${{ github.event.inputs.crawler_type == 'all' || github.event.inputs.crawler_type == '' }}
      run: |
        echo "⏳ 等待5秒后执行下一个爬虫..."
        sleep 5
        
    - name: Run DailyCode Pinpoint Crawler
      if: ${{ github.event.inputs.crawler_type == 'all' || github.event.inputs.crawler_type == 'dailycode' || github.event.inputs.crawler_type == '' }}
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🔍 开始执行 DailyCode Pinpoint 爬虫任务..."
        echo "⏰ 注意：此网站需要等待约30秒才会显示答案"
        python app/scripts/pinpoint_crawler_dailycode.py || echo "⚠️ DailyCode 爬虫执行失败"
        
    - name: Upload logs (if needed)
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: all-crawler-logs-${{ github.run_number }}
        path: |
          *.log
          logs/
        retention-days: 7

    - name: Summary
      if: always()
      run: |
        echo "📊 爬虫任务执行完成！"
        echo "- Phonenumble 爬虫: ${{ steps.phonenumble.outcome || '已跳过' }}"
        echo "- DailyCode 爬虫: ${{ steps.dailycode.outcome || '已跳过' }}"
