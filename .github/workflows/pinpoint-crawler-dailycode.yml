name: Pinpoint DailyCode Crawler

on:
  # 仅手动触发，用于测试
  workflow_dispatch:

jobs:
  crawl-pinpoint-dailycode:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-dailycode-${{ hashFiles('**/app/scripts/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-dailycode-
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r app/scripts/requirements.txt
        
    - name: Run DailyCode Pinpoint Crawler
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🔍 开始执行 DailyCode Pinpoint 爬虫任务..."
        echo "⏰ 注意：此网站需要等待约30秒才会显示答案"
        python app/scripts/pinpoint_crawler_dailycode.py
        
    - name: Upload logs (if needed)
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: dailycode-crawler-logs
        path: |
          *.log
          logs/
        retention-days: 7

    - name: Notify on success
      if: success()
      run: |
        echo "✅ DailyCode Pinpoint 爬虫任务执行成功！"
        
    - name: Notify on failure
      if: failure()
      run: |
        echo "❌ DailyCode Pinpoint 爬虫任务执行失败！"
