name: Pinpoint Daily Crawler

on:
  # 仅手动触发，用于测试
  workflow_dispatch:

jobs:
  crawl-pinpoint:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/app/scripts/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r app/scripts/requirements.txt
        
    - name: Run Pinpoint Crawler
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      run: |
        echo "🔍 开始执行 Pinpoint 爬虫任务..."
        python app/scripts/pinpoint_crawler.py
        
    - name: Upload logs (if needed)
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: crawler-logs
        path: |
          *.log
          logs/
        retention-days: 7
