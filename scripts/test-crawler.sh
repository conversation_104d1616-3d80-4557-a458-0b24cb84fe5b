#!/bin/bash

# 测试Pinpoint爬虫API脚本

# 配置
BASE_URL="http://localhost:3000"
CRAWLER_SECRET="pinpoint-crawler-secret-2025"

echo "🧪 开始测试 Pinpoint 爬虫 API..."
echo "📍 测试地址: $BASE_URL"
echo ""

# 1. 测试爬虫API健康检查
echo "1️⃣ 测试爬虫API健康检查..."
health_response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/api/crawler/pinpoint")
health_code=$(echo "$health_response" | tail -n1)
health_body=$(echo "$health_response" | sed '$d')

echo "状态码: $health_code"
echo "响应: $health_body"
echo ""

# 2. 测试Cron API健康检查
echo "2️⃣ 测试Cron API健康检查..."
cron_response=$(curl -s -w "\n%{http_code}" -X GET "$BASE_URL/api/cron/pinpoint")
cron_code=$(echo "$cron_response" | tail -n1)
cron_body=$(echo "$cron_response" | sed '$d')

echo "状态码: $cron_code"
echo "响应: $cron_body"
echo ""

# 3. 测试爬虫执行（需要密钥）
echo "3️⃣ 测试爬虫执行..."
crawler_response=$(curl -s -w "\n%{http_code}" -X POST \
  "$BASE_URL/api/crawler/pinpoint" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $CRAWLER_SECRET")

crawler_code=$(echo "$crawler_response" | tail -n1)
crawler_body=$(echo "$crawler_response" | sed '$d')

echo "状态码: $crawler_code"
echo "响应:"
echo "$crawler_body" | jq '.' 2>/dev/null || echo "$crawler_body"
echo ""

# 4. 测试Cron触发（模拟Cloudflare Cron）
echo "4️⃣ 测试Cron触发（模拟Cloudflare）..."
cron_trigger_response=$(curl -s -w "\n%{http_code}" -X POST \
  "$BASE_URL/api/cron/pinpoint" \
  -H "Content-Type: application/json" \
  -H "User-Agent: Cloudflare-Workers" \
  -H "cf-cron: test")

cron_trigger_code=$(echo "$cron_trigger_response" | tail -n1)
cron_trigger_body=$(echo "$cron_trigger_response" | sed '$d')

echo "状态码: $cron_trigger_code"
echo "响应:"
echo "$cron_trigger_body" | jq '.' 2>/dev/null || echo "$cron_trigger_body"
echo ""

# 总结
echo "📊 测试总结:"
echo "- 爬虫API健康检查: $([ "$health_code" = "200" ] && echo "✅ 通过" || echo "❌ 失败")"
echo "- Cron API健康检查: $([ "$cron_code" = "200" ] && echo "✅ 通过" || echo "❌ 失败")"
echo "- 爬虫执行测试: $([ "$crawler_code" = "200" ] && echo "✅ 通过" || echo "❌ 失败")"
echo "- Cron触发测试: $([ "$cron_trigger_code" = "200" ] && echo "✅ 通过" || echo "❌ 失败")"
echo ""

if [ "$crawler_code" = "200" ] && [ "$cron_trigger_code" = "200" ]; then
    echo "🎉 所有测试通过！爬虫功能正常工作。"
else
    echo "⚠️ 部分测试失败，请检查错误信息。"
fi
