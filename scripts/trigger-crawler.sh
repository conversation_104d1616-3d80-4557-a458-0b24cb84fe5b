#!/bin/bash

# 手动触发Pinpoint爬虫脚本

# 配置
DOMAIN="你的域名.com"  # 替换为你的实际域名
CRAWLER_SECRET="your-secure-crawler-secret-key-here"  # 替换为你的实际密钥

echo "🔍 手动触发 Pinpoint 爬虫..."

# 调用爬虫API
response=$(curl -s -w "\n%{http_code}" -X POST \
  "https://${DOMAIN}/api/crawler/pinpoint" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${CRAWLER_SECRET}")

# 分离响应体和状态码
http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

echo "HTTP状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"

if [ "$http_code" = "200" ]; then
    echo "✅ 爬虫执行成功！"
else
    echo "❌ 爬虫执行失败，状态码: $http_code"
    exit 1
fi
