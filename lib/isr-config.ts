// ISR (Incremental Static Regeneration) 配置常量

// 重新生成时间配置（秒）
export const REVALIDATE_TIMES = {
  // 答案详情页面 - 1小时重新生成
  ANSWER_PAGE: 3600,
  
  // 首页 - 30分钟重新生成（更频繁，因为可能有新答案）
  HOME_PAGE: 1800,
  
  // 历史页面 - 1小时重新生成
  HISTORY_PAGE: 3600,
  
  // 管理后台页面 - 5分钟重新生成（需要实时性）
  ADMIN_PAGE: 300,
} as const;

// 缓存标签
export const CACHE_TAGS = {
  // 所有 Pinpoint 相关数据
  PINPOINT_ANSWERS: 'pinpoint-answers',
  
  // 今日答案
  TODAY_ANSWER: 'today-answer',
  
  // 历史答案
  HISTORY_ANSWERS: 'history-answers',
  
  // 最近答案
  RECENT_ANSWERS: 'recent-answers',
  
  // 单个答案
  SINGLE_ANSWER: 'single-answer',
} as const;

// 静态生成配置
export const STATIC_GENERATION = {
  // 最大静态生成页面数量
  MAX_STATIC_PAGES: 1000,
  
  // 是否在构建时生成所有页面
  GENERATE_ALL_AT_BUILD: true,
  
  // 是否启用按需生成
  ENABLE_ON_DEMAND: true,
  
  // 错误页面重新生成间隔
  ERROR_REVALIDATE: 60,
} as const;

// 页面路径配置
export const PAGE_PATHS = {
  HOME: '/',
  HISTORY: '/history',
  ANSWER_DETAIL: (slug: string) => `/${slug}`,
  ADMIN_PINPOINT: '/admin/pinpoint',
} as const;

// 重新生成触发函数的路径配置
export const REVALIDATION_PATHS = {
  // 当新增答案时需要重新生成的路径
  ON_ANSWER_CREATE: [
    PAGE_PATHS.HOME,
    PAGE_PATHS.HISTORY,
  ],
  
  // 当更新答案时需要重新生成的路径
  ON_ANSWER_UPDATE: (slug: string) => [
    PAGE_PATHS.HOME,
    PAGE_PATHS.HISTORY,
    PAGE_PATHS.ANSWER_DETAIL(slug),
  ],
  
  // 当删除答案时需要重新生成的路径
  ON_ANSWER_DELETE: [
    PAGE_PATHS.HOME,
    PAGE_PATHS.HISTORY,
  ],
} as const;

// 导出类型
export type RevalidateTime = typeof REVALIDATE_TIMES[keyof typeof REVALIDATE_TIMES];
export type CacheTag = typeof CACHE_TAGS[keyof typeof CACHE_TAGS];

// 工具函数：获取重新生成路径
export function getRevalidationPaths(action: 'create' | 'update' | 'delete', slug?: string): string[] {
  switch (action) {
    case 'create':
      return REVALIDATION_PATHS.ON_ANSWER_CREATE;
    case 'update':
      return slug ? REVALIDATION_PATHS.ON_ANSWER_UPDATE(slug) : REVALIDATION_PATHS.ON_ANSWER_CREATE;
    case 'delete':
      return REVALIDATION_PATHS.ON_ANSWER_DELETE;
    default:
      return [];
  }
}

// 工具函数：获取缓存标签数组
export function getCacheTags(tags: CacheTag[]): string[] {
  return tags;
}

// 工具函数：生成答案页面的所有相关标签
export function getAnswerCacheTags(gameNumber: number): string[] {
  return [
    CACHE_TAGS.PINPOINT_ANSWERS,
    CACHE_TAGS.SINGLE_ANSWER,
    `${CACHE_TAGS.SINGLE_ANSWER}-${gameNumber}`,
  ];
}

// 工具函数：生成首页相关标签
export function getHomeCacheTags(): string[] {
  return [
    CACHE_TAGS.PINPOINT_ANSWERS,
    CACHE_TAGS.TODAY_ANSWER,
    CACHE_TAGS.RECENT_ANSWERS,
  ];
}

// 工具函数：生成历史页面相关标签
export function getHistoryCacheTags(): string[] {
  return [
    CACHE_TAGS.PINPOINT_ANSWERS,
    CACHE_TAGS.HISTORY_ANSWERS,
  ];
}
