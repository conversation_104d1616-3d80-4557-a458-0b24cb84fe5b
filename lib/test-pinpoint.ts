// Pinpoint 功能测试文件
// 用于验证数据库连接、数据模型和工具函数

import { 
  getAllPinpointAnswers,
  getTodayPinpointAnswer,
  getRecentPinpointAnswers,
  findPinpointAnswerBySlug,
  findPinpointAnswerByGameNumber,
  getPinpointAnswersHistory,
  getPinpointStats
} from "@/models/pinpoint";

import {
  generateUrlSlug,
  validatePinpointData,
  formatPinpointAnswer,
  generateTodayPreview,
  generateRecentPreviews,
  getNextPuzzleTime,
  calculateCountdown,
  formatCountdown
} from "@/lib/pinpoint";

// 测试数据库连接和基础查询
export async function testDatabaseConnection() {
  console.log("🔍 测试数据库连接...");
  
  try {
    // 测试获取统计信息
    const stats = await getPinpointStats();
    console.log("✅ 数据库连接成功");
    console.log("📊 统计信息:", stats);
    
    return { success: true, stats };
  } catch (error) {
    console.error("❌ 数据库连接失败:", error);
    return { success: false, error };
  }
}

// 测试数据查询功能
export async function testDataQueries() {
  console.log("🔍 测试数据查询功能...");
  
  try {
    // 测试获取所有答案
    const allAnswers = await getAllPinpointAnswers();
    console.log(`✅ 获取所有答案成功，共 ${allAnswers.length} 条`);
    
    // 测试获取今日答案
    const todayAnswer = await getTodayPinpointAnswer();
    console.log("✅ 获取今日答案:", todayAnswer ? `#${todayAnswer.game_number}` : "无");
    
    // 测试获取最近答案
    const recentAnswers = await getRecentPinpointAnswers(5);
    console.log(`✅ 获取最近答案成功，共 ${recentAnswers.length} 条`);
    
    // 测试根据游戏编号查询
    if (allAnswers.length > 0) {
      const firstAnswer = allAnswers[0];
      const answerByNumber = await findPinpointAnswerByGameNumber(firstAnswer.game_number);
      console.log(`✅ 根据游戏编号查询成功: #${answerByNumber?.game_number}`);
      
      // 测试根据slug查询
      const answerBySlug = await findPinpointAnswerBySlug(firstAnswer.url_slug);
      console.log(`✅ 根据slug查询成功: ${answerBySlug?.url_slug}`);
    }
    
    // 测试分页查询
    const historyResult = await getPinpointAnswersHistory(1, 5);
    console.log(`✅ 分页查询成功，第1页共 ${historyResult.data.length} 条，总计 ${historyResult.pagination.total} 条`);
    
    return { 
      success: true, 
      totalAnswers: allAnswers.length,
      todayAnswer: todayAnswer?.game_number,
      recentCount: recentAnswers.length
    };
  } catch (error) {
    console.error("❌ 数据查询失败:", error);
    return { success: false, error };
  }
}

// 测试工具函数
export async function testUtilityFunctions() {
  console.log("🔍 测试工具函数...");
  
  try {
    // 测试URL slug生成
    const testClues = ["book", "point", "list", "mate", "please"];
    const slug = generateUrlSlug(458, testClues);
    console.log("✅ URL slug生成:", slug);
    
    // 测试数据验证
    const validData = {
      gameNumber: 458,
      date: "2025-08-01",
      answer: "Words that come after head",
      clueWords: testClues
    };
    
    const validationResult = validatePinpointData(validData);
    console.log("✅ 数据验证 (有效数据):", validationResult.isValid ? "通过" : "失败");
    
    // 测试无效数据验证
    const invalidData = {
      gameNumber: -1,
      date: "invalid-date",
      answer: "",
      clueWords: ["only", "four", "words", "here"]
    };
    
    const invalidValidation = validatePinpointData(invalidData);
    console.log("✅ 数据验证 (无效数据):", invalidValidation.isValid ? "意外通过" : "正确拒绝");
    console.log("   错误信息:", invalidValidation.errors.map(e => e.message));
    
    // 测试倒计时功能
    const nextPuzzleTime = getNextPuzzleTime();
    const countdown = calculateCountdown(nextPuzzleTime);
    const formattedCountdown = formatCountdown(countdown);
    console.log("✅ 倒计时功能:", formattedCountdown);
    console.log("   下次发布时间:", nextPuzzleTime.toISOString());
    
    return { success: true, slug, validationPassed: validationResult.isValid };
  } catch (error) {
    console.error("❌ 工具函数测试失败:", error);
    return { success: false, error };
  }
}

// 测试数据格式转换
export async function testDataFormatting() {
  console.log("🔍 测试数据格式转换...");
  
  try {
    // 获取一个测试答案
    const answers = await getRecentPinpointAnswers(1);
    if (answers.length === 0) {
      console.log("⚠️ 没有测试数据，跳过格式转换测试");
      return { success: true, skipped: true };
    }
    
    const testAnswer = answers[0];
    
    // 测试格式化为前端格式
    const formatted = formatPinpointAnswer(testAnswer);
    console.log("✅ 数据格式转换成功");
    console.log("   原始格式游戏编号:", testAnswer.game_number);
    console.log("   格式化后游戏编号:", formatted.gameNumber);
    console.log("   线索词数组:", formatted.clues);
    
    // 测试生成预览数据
    const todayPreview = generateTodayPreview(testAnswer);
    console.log("✅ 今日预览生成:", todayPreview);
    
    const recentPreviews = generateRecentPreviews(answers);
    console.log("✅ 最近答案预览生成:", recentPreviews.length, "条");
    
    return { success: true, formatted, todayPreview };
  } catch (error) {
    console.error("❌ 数据格式转换测试失败:", error);
    return { success: false, error };
  }
}

// 运行所有测试
export async function runAllTests() {
  console.log("🚀 开始运行 Pinpoint 功能测试...\n");
  
  const results = {
    database: await testDatabaseConnection(),
    queries: await testDataQueries(),
    utilities: await testUtilityFunctions(),
    formatting: await testDataFormatting(),
  };
  
  console.log("\n📋 测试结果汇总:");
  console.log("数据库连接:", results.database.success ? "✅ 成功" : "❌ 失败");
  console.log("数据查询:", results.queries.success ? "✅ 成功" : "❌ 失败");
  console.log("工具函数:", results.utilities.success ? "✅ 成功" : "❌ 失败");
  console.log("数据格式化:", results.formatting.success ? "✅ 成功" : "❌ 失败");
  
  const allPassed = Object.values(results).every(result => result.success);
  console.log("\n🎯 总体结果:", allPassed ? "✅ 所有测试通过" : "❌ 部分测试失败");
  
  return { allPassed, results };
}

// 快速健康检查
export async function quickHealthCheck() {
  try {
    const stats = await getPinpointStats();
    const recentAnswers = await getRecentPinpointAnswers(1);
    
    return {
      healthy: true,
      stats,
      hasData: recentAnswers.length > 0,
      latestAnswer: recentAnswers[0]?.game_number || null,
    };
  } catch (error) {
    return {
      healthy: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
