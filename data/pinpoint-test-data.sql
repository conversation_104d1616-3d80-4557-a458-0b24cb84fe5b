-- LinkedIn Pinpoint 测试数据
-- 用于开发和测试阶段

-- 插入测试数据
INSERT INTO pinpoint_daily_answers (
  game_number, 
  date, 
  answer, 
  clue_word_1, 
  clue_word_2, 
  clue_word_3, 
  clue_word_4, 
  clue_word_5, 
  url_slug, 
  status
) VALUES 
-- 最新的答案 (今天)
(458, '2025-08-01', 'Words that come after "head"', 'book', 'point', 'list', 'mate', 'please', 'linkedin-pinpoint-458-book-point-list-mate-please', 'published'),

-- 昨天的答案
(457, '2025-07-31', 'Types of music genres', 'rock', 'jazz', 'blues', 'folk', 'pop', 'linkedin-pinpoint-457-rock-jazz-blues-folk-pop', 'published'),

-- 前天的答案
(456, '2025-07-30', 'Kitchen utensils', 'spoon', 'fork', 'knife', 'whisk', 'tongs', 'linkedin-pinpoint-456-spoon-fork-knife-whisk-tongs', 'published'),

-- 更多历史数据
(455, '2025-07-29', 'Things you can break', 'glass', 'heart', 'record', 'news', 'fast', 'linkedin-pinpoint-455-glass-heart-record-news-fast', 'published'),

(454, '2025-07-28', 'Words that start with "over"', 'time', 'due', 'head', 'come', 'flow', 'linkedin-pinpoint-454-time-due-head-come-flow', 'published'),

(453, '2025-07-27', 'Things in a classroom', 'desk', 'board', 'chalk', 'book', 'chair', 'linkedin-pinpoint-453-desk-board-chalk-book-chair', 'published'),

(452, '2025-07-26', 'Movie genres', 'action', 'comedy', 'drama', 'horror', 'romance', 'linkedin-pinpoint-452-action-comedy-drama-horror-romance', 'published'),

(451, '2025-07-25', 'Things you wear', 'hat', 'shirt', 'shoes', 'watch', 'ring', 'linkedin-pinpoint-451-hat-shirt-shoes-watch-ring', 'published'),

(450, '2025-07-24', 'Sports equipment', 'ball', 'bat', 'glove', 'helmet', 'net', 'linkedin-pinpoint-450-ball-bat-glove-helmet-net', 'published'),

(449, '2025-07-23', 'Weather phenomena', 'rain', 'snow', 'wind', 'storm', 'fog', 'linkedin-pinpoint-449-rain-snow-wind-storm-fog', 'published'),

(448, '2025-07-22', 'Office supplies', 'pen', 'paper', 'clip', 'tape', 'folder', 'linkedin-pinpoint-448-pen-paper-clip-tape-folder', 'published'),

(447, '2025-07-21', 'Transportation methods', 'car', 'bus', 'train', 'plane', 'bike', 'linkedin-pinpoint-447-car-bus-train-plane-bike', 'published'),

(446, '2025-07-20', 'Fruits', 'apple', 'orange', 'banana', 'grape', 'berry', 'linkedin-pinpoint-446-apple-orange-banana-grape-berry', 'published'),

(445, '2025-07-19', 'Animals', 'dog', 'cat', 'bird', 'fish', 'horse', 'linkedin-pinpoint-445-dog-cat-bird-fish-horse', 'published'),

(444, '2025-07-18', 'Colors', 'red', 'blue', 'green', 'yellow', 'black', 'linkedin-pinpoint-444-red-blue-green-yellow-black', 'published'),

-- 一些草稿状态的数据 (用于测试管理后台)
(459, '2025-08-02', 'Future answer', 'test', 'draft', 'data', 'sample', 'temp', 'linkedin-pinpoint-459-test-draft-data-sample-temp', 'draft'),

-- 一些已删除的数据 (用于测试软删除)
(443, '2025-07-17', 'Deleted answer', 'old', 'test', 'data', 'removed', 'gone', 'linkedin-pinpoint-443-old-test-data-removed-gone', 'deleted');

-- 验证插入的数据
SELECT 
  game_number,
  date,
  answer,
  clue_word_1 || ', ' || clue_word_2 || ', ' || clue_word_3 || ', ' || clue_word_4 || ', ' || clue_word_5 as clues,
  url_slug,
  status
FROM pinpoint_daily_answers 
WHERE status = 'published'
ORDER BY date DESC 
LIMIT 10;

-- 统计信息
SELECT 
  status,
  COUNT(*) as count
FROM pinpoint_daily_answers 
GROUP BY status;

-- 检查URL slug的唯一性
SELECT 
  url_slug,
  COUNT(*) as count
FROM pinpoint_daily_answers 
GROUP BY url_slug
HAVING COUNT(*) > 1;

-- 检查游戏编号的唯一性
SELECT 
  game_number,
  COUNT(*) as count
FROM pinpoint_daily_answers 
GROUP BY game_number
HAVING COUNT(*) > 1;
