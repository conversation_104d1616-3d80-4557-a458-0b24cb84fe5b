import { PinpointAnswer, PinpointAnswerFormatted, PinpointSearchParams } from "@/types/pinpoint";
import { getSupabaseClient } from "./db";

// 插入新的Pinpoint答案
export async function insertPinpointAnswer(answer: Omit<PinpointAnswer, 'id' | 'created_at' | 'updated_at'>) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .insert(answer)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

// 根据游戏编号查找答案
export async function findPinpointAnswerByGameNumber(
  gameNumber: number
): Promise<PinpointAnswer | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("game_number", gameNumber)
    .eq("status", "published")
    .single();

  if (error) {
    return null;
  }

  return data;
}

// 根据URL slug查找答案
export async function findPinpointAnswerBySlug(
  slug: string
): Promise<PinpointAnswer | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("url_slug", slug)
    .eq("status", "published")
    .single();

  if (error) {
    return null;
  }

  return data;
}

// 根据ID查找答案（管理后台用）
export async function findPinpointAnswerById(
  id: number
): Promise<PinpointAnswer | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    return null;
  }

  return data;
}

// 获取今日答案
export async function getTodayPinpointAnswer(): Promise<PinpointAnswer | null> {
  const today = new Date().toISOString().split('T')[0];
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("date", today)
    .eq("status", "published")
    .single();

  if (error) {
    return null;
  }

  return data;
}

// 获取最新的N个答案（用于首页显示）
export async function getRecentPinpointAnswers(
  limit: number = 10
): Promise<PinpointAnswer[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("status", "published")
    .order("date", { ascending: false })
    .limit(limit);

  if (error) {
    throw error;
  }

  return data || [];
}

// 获取历史答案（分页）
export async function getPinpointAnswersHistory(
  page: number = 1,
  limit: number = 20
) {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  const { data, error, count } = await supabase
    .from("pinpoint_daily_answers")
    .select("*", { count: "exact" })
    .eq("status", "published")
    .order("date", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return {
    data: data || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
    },
  };
}

// 获取所有已发布的答案（用于静态生成）
export async function getAllPinpointAnswers(): Promise<PinpointAnswer[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .select("*")
    .eq("status", "published")
    .order("date", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}

// 管理后台：获取答案列表（支持搜索和筛选）
export async function getPinpointAnswersForAdmin(
  params: PinpointSearchParams = {}
) {
  const {
    page = 1,
    limit = 20,
    search,
    status = 'all',
    dateFrom,
    dateTo,
    gameNumberFrom,
    gameNumberTo,
  } = params;

  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  let query = supabase
    .from("pinpoint_daily_answers")
    .select("*", { count: "exact" });

  // 状态筛选
  if (status !== 'all') {
    query = query.eq("status", status);
  }

  // 搜索功能
  if (search) {
    query = query.or(`answer.ilike.%${search}%,clue_word_1.ilike.%${search}%,clue_word_2.ilike.%${search}%,clue_word_3.ilike.%${search}%,clue_word_4.ilike.%${search}%,clue_word_5.ilike.%${search}%`);
  }

  // 日期范围筛选
  if (dateFrom) {
    query = query.gte("date", dateFrom);
  }
  if (dateTo) {
    query = query.lte("date", dateTo);
  }

  // 游戏编号范围筛选
  if (gameNumberFrom) {
    query = query.gte("game_number", gameNumberFrom);
  }
  if (gameNumberTo) {
    query = query.lte("game_number", gameNumberTo);
  }

  const { data, error, count } = await query
    .order("date", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return {
    data: data || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
    },
  };
}

// 更新答案
export async function updatePinpointAnswer(
  id: number,
  updates: Partial<PinpointAnswer>
) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("pinpoint_daily_answers")
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

// 软删除答案
export async function deletePinpointAnswer(id: number) {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("pinpoint_daily_answers")
    .update({ 
      status: "deleted",
      updated_at: new Date().toISOString()
    })
    .eq("id", id);

  if (error) {
    throw error;
  }

  return true;
}

// 硬删除答案（谨慎使用）
export async function hardDeletePinpointAnswer(id: number) {
  const supabase = getSupabaseClient();
  const { error } = await supabase
    .from("pinpoint_daily_answers")
    .delete()
    .eq("id", id);

  if (error) {
    throw error;
  }

  return true;
}

// 检查游戏编号是否已存在
export async function checkGameNumberExists(gameNumber: number, excludeId?: number): Promise<boolean> {
  const supabase = getSupabaseClient();
  let query = supabase
    .from("pinpoint_daily_answers")
    .select("id")
    .eq("game_number", gameNumber);

  if (excludeId) {
    query = query.neq("id", excludeId);
  }

  const { data, error } = await query.single();

  if (error) {
    return false; // 不存在
  }

  return !!data; // 存在
}

// 检查日期是否已存在
export async function checkDateExists(date: string, excludeId?: number): Promise<boolean> {
  const supabase = getSupabaseClient();
  let query = supabase
    .from("pinpoint_daily_answers")
    .select("id")
    .eq("date", date);

  if (excludeId) {
    query = query.neq("id", excludeId);
  }

  const { data, error } = await query.single();

  if (error) {
    return false; // 不存在
  }

  return !!data; // 存在
}

// 检查URL slug是否已存在
export async function checkUrlSlugExists(urlSlug: string, excludeId?: number): Promise<boolean> {
  const supabase = getSupabaseClient();
  let query = supabase
    .from("pinpoint_daily_answers")
    .select("id")
    .eq("url_slug", urlSlug);

  if (excludeId) {
    query = query.neq("id", excludeId);
  }

  const { data, error } = await query.single();

  if (error) {
    return false; // 不存在
  }

  return !!data; // 存在
}

// 获取统计信息
export async function getPinpointStats() {
  const supabase = getSupabaseClient();
  
  const [publishedResult, draftResult, deletedResult] = await Promise.all([
    supabase.from("pinpoint_daily_answers").select("id", { count: "exact" }).eq("status", "published"),
    supabase.from("pinpoint_daily_answers").select("id", { count: "exact" }).eq("status", "draft"),
    supabase.from("pinpoint_daily_answers").select("id", { count: "exact" }).eq("status", "deleted"),
  ]);

  return {
    published: publishedResult.count || 0,
    draft: draftResult.count || 0,
    deleted: deletedResult.count || 0,
    total: (publishedResult.count || 0) + (draftResult.count || 0) + (deletedResult.count || 0),
  };
}
