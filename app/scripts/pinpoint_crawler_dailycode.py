#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, timezone
import json
from supabase import create_client, Client
import urllib3
import ssl
import os
import time

PINPOINT_URL = "https://www.dailycode.com.ng/Pinpoint-hint-answer-today"

# Supabase 配置
SUPABASE_URL = os.getenv("SUPABASE_URL", "https://urjvwlqymdokurivceni.supabase.co")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

def parse_pinpoint_page(html: str) -> dict:
    soup = BeautifulSoup(html, "html.parser")

    # 获取页面标题中的日期和编号
    title = soup.find("title")
    h2 = soup.find("h2")
    date_str = None
    game_number = None

    # 从标题或H2中提取游戏编号和日期 "Today LinkedIn Pinpoint Clues & Answer #460 August 3, 2025"
    title_text = ""
    if title:
        title_text = title.get_text()
    elif h2:
        title_text = h2.get_text()
    
    if title_text:
        # 匹配游戏编号
        number_match = re.search(r"#(\d+)", title_text)
        if number_match:
            game_number = int(number_match.group(1))
        
        # 匹配日期 "August 3, 2025"
        date_match = re.search(r"(\w+\s+\d+,\s+\d{4})", title_text)
        if date_match:
            date_str_raw = date_match.group(1)
            try:
                # 转换为标准格式
                date_obj = datetime.strptime(date_str_raw, "%B %d, %Y")
                date_str = date_obj.strftime("%Y-%m-%d")
            except:
                # 如果解析失败，尝试其他格式
                try:
                    date_obj = datetime.strptime(date_str_raw, "%b %d, %Y")
                    date_str = date_obj.strftime("%Y-%m-%d")
                except:
                    date_str = None

    # 提取答案 - 这个网站的答案会在30秒后动态显示
    answer = None
    
    # 方法1: 查找"Today Answer is:"后面的内容（优先获取页面顶部的今日答案）
    answer_heading = soup.find("h3", string="Today Answer is:")
    if answer_heading:
        # 查找紧跟在标题后面的内容
        next_element = answer_heading.find_next_sibling()
        while next_element:
            if next_element.name in ['p', 'div', 'generic']:
                text = next_element.get_text().strip()
                if text and len(text) > 5 and not text.isdigit():
                    # 清理答案文本，移除感叹号等
                    answer = text.replace('!', '').strip()
                    # 确保这是今天的答案，不是历史答案
                    if "Words that come before" in answer or "Words that come after" in answer or "Things" in answer:
                        break
            next_element = next_element.find_next_sibling()

    # 方法1.5: 如果没有找到，直接在页面顶部查找今日答案
    if not answer:
        # 查找页面前半部分的内容，避免历史数据干扰
        page_content = soup.get_text()
        # 只取前2000个字符，避免历史数据
        top_content = page_content[:2000]

        # 查找今日答案的特定模式
        today_patterns = [
            r"Words that come before [\"']([^\"']+)[\"']!?",
            r"Words that come after [\"']([^\"']+)[\"']!?",
            r"Things (?:that )?(?:have|are|can|with) (.+?)!?",
            r"Types of (.+?)!?",
            r"All have (.+?)!?",
        ]

        for pattern in today_patterns:
            match = re.search(pattern, top_content, re.IGNORECASE)
            if match:
                answer = match.group(0).replace('!', '').strip()
                if len(answer) > 10:  # 确保是完整答案
                    break
    
    # 方法2: 查找包含答案的段落
    if not answer:
        # 查找包含常见答案模式的段落
        answer_patterns = [
            r"Words that come before [\"']([^\"']+)[\"']!?",
            r"Words that come after [\"']([^\"']+)[\"']!?",
            r"Things (?:that )?(?:have|are|can|with) (.+?)!?",
            r"Types of (.+?)!?",
            r"(.+?) descriptions?!?",
            r"Kinds of (.+?)!?",
            r"(.+?) buttons?!?",
            r"All have (.+?)!?",
            r"(.+?) signoffs?!?",
        ]

        page_text = soup.get_text()
        for pattern in answer_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                answer = match.group(0).replace('!', '').strip()
                # 确保答案长度合理
                if len(answer) > 5 and len(answer) < 100:
                    break
    
    # 方法3: 查找script标签中的答案数据
    if not answer:
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # 查找可能的答案模式
                answer_match = re.search(r'"answer"\s*:\s*"([^"]+)"', script.string)
                if not answer_match:
                    answer_match = re.search(r'answer["\']?\s*[:=]\s*["\']([^"\']+)["\']', script.string)
                if answer_match:
                    answer = answer_match.group(1)
                    break

    # 提取线索词 - 查找"Today Clues is:"后面的内容
    clues = []
    
    # 方法1: 查找"Today Clues is:"标题后面的内容
    clues_heading = soup.find("h3", string="Today Clues is:")
    if clues_heading:
        # 查找紧跟在标题后面的内容
        next_element = clues_heading.find_next_sibling()
        while next_element:
            if next_element.name in ['p', 'div']:
                # 查找strong标签中的线索
                strong_tags = next_element.find_all('strong')
                if strong_tags:
                    for strong in strong_tags:
                        text = strong.get_text().strip()
                        if text:
                            # 分割逗号分隔的线索
                            clue_words = [word.strip() for word in text.split(',')]
                            clues.extend(clue_words)
                    break
                else:
                    # 如果没有strong标签，直接获取文本
                    text = next_element.get_text().strip()
                    if text:
                        clue_words = [word.strip() for word in text.split(',')]
                        clues.extend(clue_words)
                        break
            next_element = next_element.find_next_sibling()
    
    # 方法2: 从页面文本中提取线索模式
    if not clues:
        page_text = soup.get_text()
        # 查找常见的线索模式 "Head, Deed, Bottom, Finish, Punch"
        clue_match = re.search(r"(?:Today Clues is:|Clues:)\s*([A-Z][a-z]+(?:,\s*[A-Z][a-z]+)*)", page_text)
        if clue_match:
            clue_text = clue_match.group(1)
            clues = [word.strip() for word in clue_text.split(',')]
    
    # 方法3: 查找页面中的单词列表
    if not clues:
        # 查找可能是线索的单词（通常是名词或动词）
        words = re.findall(r'\b[A-Z][a-z]{3,}\b', soup.get_text())
        # 过滤掉常见的非线索词
        excluded_words = {
            'Today', 'Answer', 'Pinpoint', 'LinkedIn', 'Click', 'View', 'Daily', 'Clues', 
            'Game', 'Puzzle', 'Challenge', 'August', 'July', 'June', 'May', 'April',
            'Welcome', 'Guide', 'Ultimate', 'Whether', 'Seasoned', 'Solver', 'Curious',
            'Newbie', 'Looking', 'Brainpower', 'Everything', 'Solution', 'Insightful',
            'Hints', 'Break', 'Popular', 'Playing', 'Right', 'Away', 'Remember',
            'Chill', 'Enjoy', 'Cool', 'Time', 'Haha', 'Found', 'Location', 'Just',
            'Help', 'Navigate', 'Fast', 'Previous', 'Answers', 'Here', 'Advertisement'
        }
        potential_clues = []
        for word in words:
            if word not in excluded_words and len(word) >= 3:
                potential_clues.append(word)
                if len(potential_clues) >= 10:  # 取更多候选词
                    break
        
        # 取前5个作为线索（如果找到的话）
        if potential_clues:
            clues = potential_clues[:5]

    return {
        "game_number": game_number,
        "date": date_str,
        "answer": answer,
        "clues": clues[:5]  # 只取前5个线索
    }


def generate_url_slug(game_number: int, clues: list) -> str:
    """生成 URL slug，格式：linkedin-pinpoint-{game_number}-{clue1}-{clue2}-{clue3}-{clue4}-{clue5}"""
    if not game_number or not clues:
        return ""

    # 将线索词转换为小写并连接
    clue_parts = [clue.lower() for clue in clues[:5]]  # 只取前5个线索
    slug_parts = ["linkedin", "pinpoint", str(game_number)] + clue_parts
    return "-".join(slug_parts)


def insert_to_supabase(data: dict) -> bool:
    """将数据插入到 Supabase 数据库，如果已存在则更新"""
    try:
        # 检查环境变量
        if not SUPABASE_SERVICE_ROLE_KEY:
            print("❌ 缺少 SUPABASE_SERVICE_ROLE_KEY 环境变量")
            return False
        # 创建 Supabase 客户端
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

        # 准备数据
        clues = data.get("clues", [])
        # 确保有5个线索词，不足的用空字符串填充
        while len(clues) < 5:
            clues.append("")

        url_slug = generate_url_slug(data.get("game_number"), clues)
        game_number = data.get("game_number")

        # 检查是否已存在
        existing = supabase.table("pinpoint_daily_answers").select("id").eq("game_number", game_number).execute()

        insert_data = {
            "game_number": game_number,
            "date": data.get("date"),
            "answer": data.get("answer"),
            "clue_word_1": clues[0] if len(clues) > 0 else "",
            "clue_word_2": clues[1] if len(clues) > 1 else "",
            "clue_word_3": clues[2] if len(clues) > 2 else "",
            "clue_word_4": clues[3] if len(clues) > 3 else "",
            "clue_word_5": clues[4] if len(clues) > 4 else "",
            "url_slug": url_slug,
            "status": "published"
        }

        if existing.data:
            # 记录已存在，执行更新
            result = supabase.table("pinpoint_daily_answers").update(insert_data).eq("game_number", game_number).execute()
            print(f"🔄 游戏编号 {game_number} 已存在，数据已更新")
        else:
            # 记录不存在，执行插入
            result = supabase.table("pinpoint_daily_answers").insert(insert_data).execute()
            print(f"✅ 新数据已成功插入到 Supabase，游戏编号: {game_number}")

        return True

    except Exception as e:
        print(f"❌ 操作 Supabase 失败: {str(e)}")
        return False


def fetch_pinpoint_with_wait():
    """获取 Pinpoint 数据，等待30秒让答案动态加载"""
    # 禁用 SSL 警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 配置请求会话
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    # 重试配置
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            print(f"🔄 尝试获取数据 (第 {attempt + 1}/{max_retries} 次)...")

            # 获取页面内容
            resp = session.get(PINPOINT_URL, timeout=15)
            resp.raise_for_status()
            
            # 第一次解析，检查是否已经有答案
            parsed = parse_pinpoint_page(resp.text)
            
            if parsed.get("answer") and len(parsed.get("answer", "")) > 10:
                # 如果已经获取到完整答案，直接返回
                print("✅ 答案已经可用，无需等待")
                parsed["fetched_at"] = datetime.now(timezone.utc).isoformat()
                return parsed
            else:
                # 如果答案还没有加载，等待30秒
                print("⏳ 答案尚未加载，等待30秒...")
                time.sleep(30)
                
                # 重新获取页面
                print("🔄 重新获取页面内容...")
                resp = session.get(PINPOINT_URL, timeout=15)
                resp.raise_for_status()
                
                # 重新解析
                parsed = parse_pinpoint_page(resp.text)
                parsed["fetched_at"] = datetime.now(timezone.utc).isoformat()
                
                if parsed.get("answer"):
                    print("✅ 等待后成功获取答案！")
                else:
                    print("⚠️ 等待后仍未获取到答案，但继续处理...")
                
                return parsed

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("❌ 所有重试都失败了")
                raise

        except Exception as e:
            print(f"❌ 未知错误 (尝试 {attempt + 1}): {str(e)}")
            if attempt < max_retries - 1:
                print(f"⏳ {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("❌ 所有重试都失败了")
                raise


if __name__ == "__main__":
    print("🔍 正在获取 DailyCode Pinpoint 游戏数据...")
    print("⏰ 注意：此网站的答案需要等待约30秒才会显示")
    
    data = fetch_pinpoint_with_wait()

    print("📊 获取到的数据:")
    print(json.dumps(data, ensure_ascii=False, indent=2))

    # 检查数据完整性
    if data.get("game_number") and data.get("date") and data.get("clues"):
        print("\n💾 正在插入数据到 Supabase...")
        success = insert_to_supabase(data)
        if success:
            print("🎉 任务完成！")
        else:
            print("⚠️ 数据插入失败，请检查错误信息")
    else:
        print("⚠️ 数据不完整，跳过数据库插入")
        missing_fields = []
        if not data.get("game_number"):
            missing_fields.append("game_number")
        if not data.get("date"):
            missing_fields.append("date")
        if not data.get("answer"):
            missing_fields.append("answer")
        if not data.get("clues"):
            missing_fields.append("clues")
        print(f"缺失字段: {', '.join(missing_fields)}")
