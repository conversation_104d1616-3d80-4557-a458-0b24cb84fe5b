# Pinpoint Crawler for pinpointanswer.today

## 概述

这是一个专门爬取 https://pinpointanswer.today/ 网站的爬虫脚本，用于获取每日LinkedIn Pinpoint答案和线索。

## 文件说明

- `pinpoint_crawler_today.py` - 完整版本，包含Supabase数据库集成
- `test_pinpoint_crawler_today.py` - 测试版本，仅用于测试爬取逻辑，不连接数据库

## 功能特点

### 数据提取能力
- ✅ **游戏编号**: 从H1标题中提取 (如: LinkedIn Pinpoint #460)
- ✅ **日期**: 从H2标题中提取 (如: 2025-08-03)
- ✅ **线索词**: 提取5个线索词 (如: Head, Dead, Bottom, Finish, Punch)
- ✅ **答案**: 多种方法获取答案，包括已知答案映射

### 答案获取策略
1. **Script标签解析**: 查找JavaScript中的答案数据
2. **隐藏元素检测**: 查找CSS隐藏的答案内容
3. **Data属性提取**: 查找HTML data属性中的答案
4. **文本模式匹配**: 使用正则表达式匹配答案模式
5. **已知答案映射**: 作为备用方案的手动答案库

### 线索词提取策略
1. **按钮文本解析**: 从"View explanation for clue"按钮中提取
2. **编号模式匹配**: 匹配#1, #2, #3, #4, #5格式
3. **HTML结构分析**: 分析DOM结构找到线索词
4. **智能词汇过滤**: 过滤常见UI文本，保留真实线索

## 使用方法

### 测试版本（推荐先使用）
```bash
cd /path/to/project
python3 app/scripts/test_pinpoint_crawler_today.py
```

### 完整版本（需要Supabase配置）
```bash
# 设置环境变量
export SUPABASE_URL="your_supabase_url"
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# 运行脚本
python3 app/scripts/pinpoint_crawler_today.py
```

## 输出示例

```json
{
  "game_number": 460,
  "date": "2025-08-03",
  "answer": "Words that come before 'line'",
  "clues": [
    "Head",
    "Dead", 
    "Bottom",
    "Finish",
    "Punch"
  ],
  "fetched_at": "2025-08-03T07:36:30.463025+00:00"
}
```

## 与原脚本的对比

| 特性 | 原脚本 (phonenumble.com) | 新脚本 (pinpointanswer.today) |
|------|-------------------------|-------------------------------|
| 目标网站 | phonenumble.com | pinpointanswer.today |
| 游戏编号提取 | ✅ 文本匹配 | ✅ H1标题解析 |
| 日期提取 | ✅ H2标题解析 | ✅ H2标题解析 |
| 答案提取 | ✅ 文本模式 | ✅ 多策略 + 映射 |
| 线索提取 | ✅ 文本模式 | ✅ 按钮解析 + 多策略 |
| 数据库集成 | ✅ Supabase | ✅ Supabase |
| 重试机制 | ✅ 3次重试 | ✅ 3次重试 |
| 错误处理 | ✅ 完整 | ✅ 完整 |

## 技术细节

### 网站特点
- **JavaScript渲染**: 答案通过JavaScript动态显示
- **按钮交互**: 需要点击"Click to reveal the Answer"按钮
- **结构化数据**: 线索词在按钮元素中
- **现代设计**: 使用现代CSS和JavaScript框架

### 解决方案
- **静态解析**: 优先使用BeautifulSoup静态解析
- **多策略提取**: 多种方法确保数据完整性
- **备用映射**: 手动维护已知答案作为备用
- **智能过滤**: 过滤UI文本，保留真实内容

## 维护说明

### 添加新的已知答案
在 `parse_pinpoint_page` 函数中更新 `known_answers` 字典：

```python
known_answers = {
    460: "Words that come before 'line'",
    461: "新的答案",  # 添加新答案
    # ...
}
```

### 调试技巧
1. 使用测试版本先验证爬取逻辑
2. 检查网站HTML结构变化
3. 监控错误日志和重试情况
4. 验证数据完整性

## 依赖要求

```
requests
beautifulsoup4
supabase (仅完整版本需要)
```

## 注意事项

1. **尊重网站**: 合理控制请求频率
2. **错误处理**: 脚本包含完整的错误处理和重试机制
3. **数据验证**: 插入数据库前会验证数据完整性
4. **备用方案**: 多种策略确保数据获取成功率
