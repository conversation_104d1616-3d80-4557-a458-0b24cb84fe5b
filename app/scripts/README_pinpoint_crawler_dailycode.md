# Pinpoint Crawler for dailycode.com.ng

## 概述

这是一个专门爬取 https://www.dailycode.com.ng/Pinpoint-hint-answer-today 网站的爬虫脚本，用于获取每日LinkedIn Pinpoint答案和线索。

## 文件说明

- `pinpoint_crawler_dailycode.py` - 完整版本，包含Supabase数据库集成
- `test_pinpoint_crawler_dailycode.py` - 测试版本，仅用于测试爬取逻辑，不连接数据库

## 网站特点

### 🕐 **动态加载机制**
- 答案需要等待约30秒后才会动态显示
- 脚本会自动检测答案是否已加载，如未加载则等待30秒

### 📋 **页面结构**
- **游戏编号**: 从页面标题提取 `#460`
- **日期**: 从页面标题提取 `August 3, 2025` → `2025-08-03`
- **答案**: 位于 `### Today Answer is:` 标题后
- **线索**: 位于 `### Today Clues is:` 标题后，逗号分隔

## 功能特点

### ✅ **智能等待机制**
```python
# 第一次检查是否已有答案
if parsed.get("answer") and len(parsed.get("answer", "")) > 10:
    print("✅ 答案已经可用，无需等待")
    return parsed
else:
    print("⏳ 答案尚未加载，等待30秒...")
    time.sleep(30)
```

### 🎯 **精确数据提取**
- **游戏编号**: 正则匹配 `#(\d+)`
- **日期转换**: `August 3, 2025` → `2025-08-03`
- **答案清理**: 移除感叹号，处理换行和多余内容
- **线索分割**: 逗号分隔的线索词自动分割

### 🔄 **容错机制**
- 多种H3标题匹配方式
- 3次重试机制，指数退避
- 完整的错误处理和日志

## 使用方法

### 测试版本（推荐先使用）
```bash
cd /path/to/project
python3 app/scripts/test_pinpoint_crawler_dailycode.py
```

### 完整版本（需要Supabase配置）
```bash
# 设置环境变量
export SUPABASE_URL="your_supabase_url"
export SUPABASE_SERVICE_ROLE_KEY="your_service_role_key"

# 运行脚本
python3 app/scripts/pinpoint_crawler_dailycode.py
```

## 输出示例

```json
{
  "game_number": 460,
  "date": "2025-08-03",
  "answer": "Words that come before \"line\"",
  "clues": [
    "Head",
    "Deed", 
    "Bottom",
    "Finish",
    "Punch"
  ],
  "fetched_at": "2025-08-03T07:56:18.408565+00:00"
}
```

## 与其他爬虫的对比

| 特性 | dailycode.com.ng | pinpointanswer.today | phonenumble.com |
|------|------------------|---------------------|-----------------|
| 等待时间 | 30秒动态加载 | 无需等待 | 无需等待 |
| 答案位置 | `### Today Answer is:` | 按钮点击后显示 | 文本模式 |
| 线索格式 | 逗号分隔 | 按钮元素 | 文本模式 |
| 页面结构 | 简单清晰 | JavaScript重度 | 静态HTML |
| 解析难度 | 简单 | 中等 | 简单 |

## 技术实现

### 核心解析逻辑
```python
# 答案解析
answer_heading = soup.find("h3", string="Today Answer is:")
if answer_heading:
    next_element = answer_heading.find_next_sibling()
    text = next_element.get_text().strip()
    answer = text.replace('!', '').strip()

# 线索解析  
clues_heading = soup.find("h3", string="Today Clues is:")
if clues_heading:
    next_element = clues_heading.find_next_sibling()
    text = next_element.get_text().strip()
    clues = [word.strip() for word in text.split(',')]
```

### 智能等待策略
```python
def fetch_pinpoint_with_wait():
    # 第一次获取
    parsed = parse_pinpoint_page(resp.text)
    
    if parsed.get("answer") and len(parsed.get("answer", "")) > 10:
        return parsed  # 答案已可用
    else:
        time.sleep(30)  # 等待30秒
        # 重新获取页面
        resp = session.get(PINPOINT_URL, timeout=15)
        parsed = parse_pinpoint_page(resp.text)
        return parsed
```

## 维护说明

### 常见问题
1. **答案未获取到**: 可能需要等待更长时间，或者页面结构发生变化
2. **线索不完整**: 检查逗号分隔格式是否正确
3. **日期解析失败**: 检查日期格式是否变化

### 调试技巧
1. 使用测试版本先验证解析逻辑
2. 检查H3标题的实际文本内容
3. 验证答案和线索的HTML结构

## 依赖要求

```
requests
beautifulsoup4
supabase (仅完整版本需要)
```

## 注意事项

1. **等待时间**: 脚本会自动处理30秒等待，无需手动干预
2. **网络稳定**: 确保网络连接稳定，避免等待期间断网
3. **频率控制**: 建议每天运行一次，避免频繁请求
4. **数据验证**: 插入数据库前会验证数据完整性
