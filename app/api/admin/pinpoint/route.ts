import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { 
  insertPinpointAnswer, 
  getPinpointAnswersForAdmin,
  checkGameNumberExists,
  checkDateExists,
  checkUrlSlugExists
} from "@/models/pinpoint";
import { 
  validatePinpointData, 
  formatToDatabase,
  generateUrlSlug 
} from "@/lib/pinpoint";
import { revalidateTag, revalidatePath } from "next/cache";
import { CACHE_TAGS, getRevalidationPaths } from "@/lib/isr-config";

// 获取答案列表 (管理后台)
export async function GET(req: NextRequest) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("未授权", 401);
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || undefined;
    const status = searchParams.get("status") || "all";
    const dateFrom = searchParams.get("dateFrom") || undefined;
    const dateTo = searchParams.get("dateTo") || undefined;
    const gameNumberFrom = searchParams.get("gameNumberFrom") 
      ? parseInt(searchParams.get("gameNumberFrom")!) 
      : undefined;
    const gameNumberTo = searchParams.get("gameNumberTo") 
      ? parseInt(searchParams.get("gameNumberTo")!) 
      : undefined;

    const result = await getPinpointAnswersForAdmin({
      page,
      limit: Math.min(limit, 100), // 限制最大每页数量
      search,
      status: status as any,
      dateFrom,
      dateTo,
      gameNumberFrom,
      gameNumberTo,
    });

    return respData(result);
  } catch (error) {
    console.error("获取答案列表失败:", error);
    return respErr("获取答案列表失败");
  }
}

// 创建新答案
export async function POST(req: NextRequest) {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("未授权", 401);
    }

    const body = await req.json();
    
    // 验证数据格式
    const validation = validatePinpointData(body);
    if (!validation.isValid) {
      return respErr(`数据验证失败: ${validation.errors.map(e => e.message).join(", ")}`);
    }

    // 检查游戏编号是否已存在
    const gameNumberExists = await checkGameNumberExists(body.gameNumber);
    if (gameNumberExists) {
      return respErr(`游戏编号 #${body.gameNumber} 已存在`);
    }

    // 检查日期是否已存在
    const dateExists = await checkDateExists(body.date);
    if (dateExists) {
      return respErr(`日期 ${body.date} 已存在答案`);
    }

    // 生成URL slug并检查是否已存在
    const urlSlug = generateUrlSlug(body.gameNumber, body.clueWords);
    const slugExists = await checkUrlSlugExists(urlSlug);
    if (slugExists) {
      return respErr(`URL slug 已存在，请检查游戏编号和线索词`);
    }

    // 转换为数据库格式
    const pinpointAnswer = formatToDatabase(body);

    // 插入数据库
    const result = await insertPinpointAnswer(pinpointAnswer);

    // 触发相关页面重新生成
    try {
      // 重新生成缓存标签
      revalidateTag(CACHE_TAGS.PINPOINT_ANSWERS);
      revalidateTag(CACHE_TAGS.TODAY_ANSWER);
      revalidateTag(CACHE_TAGS.RECENT_ANSWERS);
      revalidateTag(CACHE_TAGS.HISTORY_ANSWERS);

      // 重新生成相关页面
      const pathsToRevalidate = getRevalidationPaths('create');
      for (const path of pathsToRevalidate) {
        revalidatePath(path);
      }

      // 重新生成新创建的答案页面
      revalidatePath(`/${result.url_slug}`);

      console.log(`✅ 页面重新生成完成: ${pathsToRevalidate.join(", ")}, /${result.url_slug}`);
    } catch (revalidateError) {
      console.error("页面重新生成失败:", revalidateError);
      // 不影响主要功能，只记录错误
    }

    return respData({
      ...result,
      message: "答案创建成功，相关页面正在重新生成"
    });
  } catch (error) {
    console.error("创建答案失败:", error);
    return respErr("创建答案失败");
  }
}
