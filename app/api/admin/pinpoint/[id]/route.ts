import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { 
  findPinpointAnswerById,
  updatePinpointAnswer,
  deletePinpointAnswer,
  checkGameNumberExists,
  checkDateExists,
  checkUrlSlugExists
} from "@/models/pinpoint";
import { 
  validatePinpointData, 
  formatToDatabase,
  generateUrlSlug 
} from "@/lib/pinpoint";
import { revalidateTag, revalidatePath } from "next/cache";
import { CACHE_TAGS, getRevalidationPaths } from "@/lib/isr-config";

// 获取单个答案详情
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 临时注释登录校验，方便测试
    // const userUuid = await getUserUuid();
    // if (!userUuid) {
    //   return respErr("未授权", 401);
    // }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的答案ID");
    }

    const answer = await findPinpointAnswerById(id);
    if (!answer) {
      return respErr("答案不存在");
    }

    return respData(answer);
  } catch (error) {
    console.error("获取答案详情失败:", error);
    return respErr("获取答案详情失败");
  }
}

// 更新答案
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 临时注释登录校验，方便测试
    // const userUuid = await getUserUuid();
    // if (!userUuid) {
    //   return respErr("未授权", 401);
    // }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的答案ID");
    }

    // 检查答案是否存在
    const existingAnswer = await findPinpointAnswerById(id);
    if (!existingAnswer) {
      return respErr("答案不存在");
    }

    const body = await req.json();
    
    // 验证数据格式
    const validation = validatePinpointData(body);
    if (!validation.isValid) {
      return respErr(`数据验证失败: ${validation.errors.map(e => e.message).join(", ")}`);
    }

    // 检查游戏编号是否已存在（排除当前记录）
    if (body.gameNumber !== existingAnswer.game_number) {
      const gameNumberExists = await checkGameNumberExists(body.gameNumber, id);
      if (gameNumberExists) {
        return respErr(`游戏编号 #${body.gameNumber} 已存在`);
      }
    }

    // 检查日期是否已存在（排除当前记录）
    if (body.date !== existingAnswer.date) {
      const dateExists = await checkDateExists(body.date, id);
      if (dateExists) {
        return respErr(`日期 ${body.date} 已存在答案`);
      }
    }

    // 生成新的URL slug并检查是否已存在（排除当前记录）
    const newUrlSlug = generateUrlSlug(body.gameNumber, body.clueWords);
    if (newUrlSlug !== existingAnswer.url_slug) {
      const slugExists = await checkUrlSlugExists(newUrlSlug, id);
      if (slugExists) {
        return respErr(`URL slug 已存在，请检查游戏编号和线索词`);
      }
    }

    // 转换为数据库格式
    const updateData = formatToDatabase(body);

    // 更新数据库
    const result = await updatePinpointAnswer(id, updateData);

    // 触发相关页面重新生成
    try {
      // 重新生成缓存标签
      revalidateTag(CACHE_TAGS.PINPOINT_ANSWERS);
      revalidateTag(CACHE_TAGS.TODAY_ANSWER);
      revalidateTag(CACHE_TAGS.RECENT_ANSWERS);
      revalidateTag(CACHE_TAGS.HISTORY_ANSWERS);
      revalidateTag(`${CACHE_TAGS.SINGLE_ANSWER}-${body.gameNumber}`);

      // 重新生成相关页面
      const pathsToRevalidate = getRevalidationPaths('update', result.url_slug);
      for (const path of pathsToRevalidate) {
        revalidatePath(path);
      }

      // 如果URL slug发生变化，需要重新生成旧的页面路径
      if (newUrlSlug !== existingAnswer.url_slug) {
        revalidatePath(`/${existingAnswer.url_slug}`);
      }

      console.log(`✅ 页面重新生成完成: ${pathsToRevalidate.join(", ")}`);
    } catch (revalidateError) {
      console.error("页面重新生成失败:", revalidateError);
      // 不影响主要功能，只记录错误
    }

    return respData({
      ...result,
      message: "答案更新成功，相关页面正在重新生成"
    });
  } catch (error) {
    console.error("更新答案失败:", error);
    return respErr("更新答案失败");
  }
}

// 删除答案（软删除）
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 临时注释登录校验，方便测试
    // const userUuid = await getUserUuid();
    // if (!userUuid) {
    //   return respErr("未授权", 401);
    // }

    const { id: idParam } = await params;
    const id = parseInt(idParam);
    if (isNaN(id)) {
      return respErr("无效的答案ID");
    }

    // 检查答案是否存在
    const existingAnswer = await findPinpointAnswerById(id);
    if (!existingAnswer) {
      return respErr("答案不存在");
    }

    // 执行软删除
    await deletePinpointAnswer(id);

    // 触发相关页面重新生成
    try {
      // 重新生成缓存标签
      revalidateTag(CACHE_TAGS.PINPOINT_ANSWERS);
      revalidateTag(CACHE_TAGS.TODAY_ANSWER);
      revalidateTag(CACHE_TAGS.RECENT_ANSWERS);
      revalidateTag(CACHE_TAGS.HISTORY_ANSWERS);
      revalidateTag(`${CACHE_TAGS.SINGLE_ANSWER}-${existingAnswer.game_number}`);

      // 重新生成相关页面
      const pathsToRevalidate = getRevalidationPaths('delete');
      for (const path of pathsToRevalidate) {
        revalidatePath(path);
      }

      // 重新生成被删除的答案页面（将显示404）
      revalidatePath(`/${existingAnswer.url_slug}`);

      console.log(`✅ 页面重新生成完成: ${pathsToRevalidate.join(", ")}`);
    } catch (revalidateError) {
      console.error("页面重新生成失败:", revalidateError);
      // 不影响主要功能，只记录错误
    }

    return respData({
      message: "答案删除成功，相关页面正在重新生成",
      deletedId: id,
      deletedGameNumber: existingAnswer.game_number
    });
  } catch (error) {
    console.error("删除答案失败:", error);
    return respErr("删除答案失败");
  }
}
