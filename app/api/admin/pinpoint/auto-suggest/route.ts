import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { getLatestPinpointAnswer } from "@/models/pinpoint";

// 自动建议下一个游戏编号和日期
export async function GET(req: NextRequest) {
  try {
    // 获取最新的答案
    const latestAnswer = await getLatestPinpointAnswer();
    
    // 计算下一个游戏编号
    const nextGameNumber = latestAnswer ? latestAnswer.game_number + 1 : 1;
    
    // 计算下一个日期（今天或明天）
    const today = new Date();
    const nextDate = new Date(today);
    
    // 如果最新答案是今天的，则建议明天
    if (latestAnswer && latestAnswer.date === today.toISOString().split('T')[0]) {
      nextDate.setDate(nextDate.getDate() + 1);
    }
    
    const suggestions = {
      gameNumber: nextGameNumber,
      date: nextDate.toISOString().split('T')[0],
      lastAnswer: latestAnswer ? {
        gameNumber: latestAnswer.game_number,
        date: latestAnswer.date,
        answer: latestAnswer.answer
      } : null
    };

    return respData(suggestions);
  } catch (error) {
    console.error("获取建议失败:", error);
    return respErr("获取建议失败");
  }
}
