import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { runAllTests, quickHealthCheck } from "@/lib/test-pinpoint";

// 运行完整测试
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const testType = searchParams.get("type") || "quick";

    if (testType === "full") {
      // 运行完整测试
      const testResults = await runAllTests();
      
      return respData({
        message: "Pinpoint 功能测试完成",
        allPassed: testResults.allPassed,
        results: testResults.results,
        timestamp: new Date().toISOString(),
      });
    } else {
      // 快速健康检查
      const healthCheck = await quickHealthCheck();
      
      return respData({
        message: "Pinpoint 健康检查完成",
        healthy: healthCheck.healthy,
        stats: healthCheck.stats,
        hasData: healthCheck.hasData,
        latestAnswer: healthCheck.latestAnswer,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error("Pinpoint 测试失败:", error);
    return respErr("测试执行失败");
  }
}

// 测试数据库连接
export async function POST(req: NextRequest) {
  try {
    const { testDatabaseConnection } = await import("@/lib/test-pinpoint");
    const result = await testDatabaseConnection();
    
    if (result.success) {
      return respData({
        message: "数据库连接测试成功",
        stats: result.stats,
        timestamp: new Date().toISOString(),
      });
    } else {
      return respErr("数据库连接测试失败");
    }
  } catch (error) {
    console.error("数据库连接测试失败:", error);
    return respErr("数据库连接测试执行失败");
  }
}
