import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { insertPinpointAnswer, findPinpointAnswerByGameNumber } from "@/models/pinpoint";
import { generateUrlSlug } from "@/lib/pinpoint";
import { revalidateTag } from "next/cache";

const PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/";

// 只允许特定的请求来源或密钥
const CRAWLER_SECRET = process.env.CRAWLER_SECRET || "your-secret-key";

export async function POST(req: NextRequest) {
  try {
    // 验证请求权限
    const authHeader = req.headers.get("authorization");
    const providedSecret = authHeader?.replace("Bearer ", "");
    
    if (providedSecret !== CRAWLER_SECRET) {
      return respErr("未授权的请求");
    }

    console.log('🔍 开始获取 Pinpoint 游戏数据...');
    
    // 获取网页内容
    const response = await fetch(PINPOINT_URL, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const html = await response.text();
    const data = parsePinpointPage(html);
    data.fetched_at = new Date().toISOString();
    
    console.log('📊 获取到的数据:', JSON.stringify(data, null, 2));
    
    // 检查数据完整性
    if (!data.game_number || !data.date || !data.answer || !data.clues || data.clues.length === 0) {
      const missingFields = [];
      if (!data.game_number) missingFields.push('game_number');
      if (!data.date) missingFields.push('date');
      if (!data.answer) missingFields.push('answer');
      if (!data.clues || data.clues.length === 0) missingFields.push('clues');
      
      return respErr(`数据不完整，缺失字段: ${missingFields.join(', ')}`);
    }

    console.log('💾 正在检查和插入数据到 Supabase...');
    
    // 检查是否已存在
    const existingAnswer = await findPinpointAnswerByGameNumber(data.game_number);
    
    // 准备数据
    const clues = data.clues || [];
    while (clues.length < 5) {
      clues.push("");
    }
    
    const urlSlug = generateUrlSlug(data.game_number, clues);
    
    const insertData = {
      game_number: data.game_number,
      date: data.date,
      answer: data.answer,
      clue_word_1: clues[0] || "",
      clue_word_2: clues[1] || "",
      clue_word_3: clues[2] || "",
      clue_word_4: clues[3] || "",
      clue_word_5: clues[4] || "",
      url_slug: urlSlug,
      status: "published" as const
    };

    let result;
    let isUpdate = false;

    if (existingAnswer) {
      // 记录已存在，检查是否需要更新
      const hasChanges = 
        existingAnswer.answer !== data.answer ||
        existingAnswer.clue_word_1 !== clues[0] ||
        existingAnswer.clue_word_2 !== clues[1] ||
        existingAnswer.clue_word_3 !== clues[2] ||
        existingAnswer.clue_word_4 !== clues[3] ||
        existingAnswer.clue_word_5 !== clues[4];

      if (hasChanges) {
        // 需要更新
        const { updatePinpointAnswer } = await import("@/models/pinpoint");
        result = await updatePinpointAnswer(existingAnswer.id!, insertData);
        isUpdate = true;
        console.log(`🔄 游戏编号 ${data.game_number} 已存在，数据已更新`);
      } else {
        // 数据相同，无需更新
        console.log(`✅ 游戏编号 ${data.game_number} 数据已是最新，无需更新`);
        return respData({
          success: true,
          message: "数据已是最新，无需更新",
          data: data,
          existing: true
        });
      }
    } else {
      // 记录不存在，执行插入
      result = await insertPinpointAnswer(insertData);
      console.log(`✅ 新数据已成功插入到 Supabase，游戏编号: ${data.game_number}`);
    }

    // 触发相关页面重新生成
    try {
      revalidateTag('pinpoint-answers');
      revalidateTag('today-answer');
      revalidateTag('history-answers');
      console.log('✅ 页面缓存已刷新');
    } catch (error) {
      console.log('⚠️ 页面缓存刷新失败:', error);
    }

    return respData({
      success: true,
      message: isUpdate ? "数据更新成功" : "数据插入成功",
      data: data,
      result: result,
      isUpdate: isUpdate
    });

  } catch (error) {
    console.error('❌ 爬虫执行失败:', error);
    return respErr(`爬虫执行失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

function parsePinpointPage(html: string) {
  let gameNumber = null;
  let dateStr = null;
  let answer = null;
  let clues: string[] = [];
  
  // 1. 提取日期 - 从标题 "Today's Pinpoint Answer (2025-08-01)" 中提取
  const dateMatch = html.match(/Today.s Pinpoint Answer\s*\((\d{4}-\d{1,2}-\d{1,2})\)/i);
  if (dateMatch) {
    dateStr = dateMatch[1];
  }
  
  // 2. 提取游戏编号 - 匹配 "pinpoint game number is 458" 格式
  const gameMatch = html.match(/pinpoint game number is (\d+)/i);
  if (gameMatch) {
    gameNumber = parseInt(gameMatch[1]);
  }
  
  // 3. 提取答案 - 从隐藏的div中提取 <div id="mainAnswer" class="answer-box" style="display: none;">答案</div>
  const answerMatch = html.match(/<div[^>]*id="mainAnswer"[^>]*>(.*?)<\/div>/i);
  if (answerMatch) {
    // 清理HTML标签和特殊字符
    answer = answerMatch[1]
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/&quot;/g, '"') // 替换HTML实体
      .replace(/&#8217;/g, "'") // 替换HTML实体
      .replace(/&amp;/g, '&') // 替换HTML实体
      .trim();
  }
  
  // 4. 提取线索词 - 查找 "Pinpoint Clues :" 后面的内容
  // 先尝试找到包含线索的部分
  const cluesSection = html.match(/Pinpoint Clues\s*:([\s\S]*?)(?=<\/div>|<p|$)/i);
  if (cluesSection) {
    const cluesHtml = cluesSection[1];

    // 方法1: 查找列表项 <li>
    const listItems = cluesHtml.match(/<li[^>]*>(.*?)<\/li>/gi);
    if (listItems && listItems.length > 0) {
      clues = listItems.map(item =>
        item.replace(/<[^>]*>/g, '').trim()
      ).filter(clue => clue.length > 0).slice(0, 5);
    } else {
      // 方法2: 查找段落或其他格式
      const paragraphs = cluesHtml.match(/<p[^>]*>(.*?)<\/p>/gi);
      if (paragraphs && paragraphs.length > 0) {
        clues = paragraphs.map(p =>
          p.replace(/<[^>]*>/g, '').trim()
        ).filter(clue => clue.length > 0).slice(0, 5);
      } else {
        // 方法3: 按行分割
        const lines = cluesHtml
          .replace(/<[^>]*>/g, '\n') // 将HTML标签替换为换行
          .split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0 && !line.match(/^(Pinpoint|Clues|:)$/i));
        clues = lines.slice(0, 5);
      }
    }
  }

  // 如果还没找到线索，尝试其他方法
  if (clues.length === 0) {
    // 查找可能的线索格式
    const alternativeClues = html.match(/(?:clue|hint|word).*?:\s*([^\n<]+)/gi);
    if (alternativeClues) {
      clues = alternativeClues.map(clue =>
        clue.replace(/^.*?:\s*/, '').trim()
      ).slice(0, 5);
    }
  }
  
  return {
    game_number: gameNumber,
    date: dateStr,
    answer: answer,
    clues: clues
  };
}

// GET方法用于健康检查
export async function GET() {
  return respData({
    status: "Pinpoint Crawler API is running",
    timestamp: new Date().toISOString()
  });
}
