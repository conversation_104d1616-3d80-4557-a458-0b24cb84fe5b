import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";

const PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/";
const CRAWLER_SECRET = process.env.CRAWLER_SECRET || "pinpoint-crawler-secret-2025";

export async function POST(req: NextRequest) {
  try {
    // 验证请求权限
    const authHeader = req.headers.get("authorization");
    const providedSecret = authHeader?.replace("Bearer ", "");
    
    if (providedSecret !== CRAWLER_SECRET) {
      return respErr("未授权的请求");
    }

    console.log('🔍 获取完整的网页内容进行分析...');
    
    // 获取网页内容
    const response = await fetch(PINPOINT_URL, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const html = await response.text();
    
    // 查找包含答案和线索的具体内容
    const lines = html.split('\n');
    const relevantContent = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 查找包含关键词的行及其前后几行
      if (line.toLowerCase().includes('pinpoint today answer') ||
          line.toLowerCase().includes('pinpoint clues') ||
          line.toLowerCase().includes('game number is') ||
          line.includes('2025-08-01') ||
          /today.s pinpoint answer/i.test(line)) {
        
        // 收集前后5行的内容
        const start = Math.max(0, i - 5);
        const end = Math.min(lines.length - 1, i + 5);
        
        for (let j = start; j <= end; j++) {
          const contextLine = lines[j].trim();
          if (contextLine) {
            relevantContent.push({
              lineNumber: j + 1,
              content: contextLine,
              isTarget: j === i
            });
          }
        }
      }
    }
    
    // 去重
    const uniqueContent = relevantContent.filter((item, index, self) => 
      index === self.findIndex(t => t.lineNumber === item.lineNumber)
    ).sort((a, b) => a.lineNumber - b.lineNumber);
    
    // 尝试不同的解析方法
    const parseAttempts = {
      // 方法1：查找包含日期的标题
      dateFromTitle: html.match(/Today.s Pinpoint Answer\s*\(([^)]+)\)/i),
      
      // 方法2：查找所有日期格式
      allDates: html.match(/\d{4}-\d{1,2}-\d{1,2}/g),
      
      // 方法3：查找游戏编号
      gameNumber: html.match(/pinpoint game number is (\d+)/i),
      
      // 方法4：查找答案内容（更宽松的匹配）
      answerContent: html.match(/Pinpoint Today Answer\s*:?\s*([^<\n]+)/i),
      
      // 方法5：查找线索内容
      cluesContent: html.match(/Pinpoint Clues\s*:?\s*([\s\S]*?)(?=<\/|$)/i),
      
      // 方法6：查找包含答案的段落
      answerParagraph: html.match(/<p[^>]*>.*?Pinpoint Today Answer.*?<\/p>/is),
      
      // 方法7：查找包含线索的段落
      cluesParagraph: html.match(/<p[^>]*>.*?Pinpoint Clues.*?<\/p>/is),
    };
    
    return respData({
      message: "详细内容分析完成",
      relevantContent: uniqueContent.slice(0, 50), // 限制返回数量
      parseAttempts: parseAttempts,
      totalLines: lines.length,
      contentLength: html.length
    });

  } catch (error) {
    console.error('❌ 内容分析失败:', error);
    return respErr(`内容分析失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// GET方法用于健康检查
export async function GET() {
  return respData({
    status: "Pinpoint Content Debug API is running",
    timestamp: new Date().toISOString()
  });
}
