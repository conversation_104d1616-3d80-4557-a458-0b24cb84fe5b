import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";

const PINPOINT_URL = "https://phonenumble.com/pinpoint-answer-today/";
const CRAWLER_SECRET = process.env.CRAWLER_SECRET || "pinpoint-crawler-secret-2025";

export async function POST(req: NextRequest) {
  try {
    // 验证请求权限
    const authHeader = req.headers.get("authorization");
    const providedSecret = authHeader?.replace("Bearer ", "");
    
    if (providedSecret !== CRAWLER_SECRET) {
      return respErr("未授权的请求");
    }

    console.log('🔍 开始调试 Pinpoint 网页内容...');
    
    // 获取网页内容
    const response = await fetch(PINPOINT_URL, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const html = await response.text();
    
    // 调试：显示HTML的关键部分
    const debugInfo = {
      url: PINPOINT_URL,
      status: response.status,
      contentLength: html.length,
      
      // 查找包含日期的部分
      dateMatches: {
        pattern1: html.match(/Today.s Pinpoint Answer\s*\([^)]+\)/g),
        pattern2: html.match(/\d{4}-\d{1,2}-\d{1,2}/g),
        pattern3: html.match(/Today.*Answer.*\(/g),
      },
      
      // 查找包含游戏编号的部分
      gameNumberMatches: {
        pattern1: html.match(/pinpoint game number is \d+/gi),
        pattern2: html.match(/game number.*\d+/gi),
        pattern3: html.match(/number.*\d+/gi),
      },
      
      // 查找包含答案的部分
      answerMatches: {
        pattern1: html.match(/Pinpoint Today Answer\s*:[^<\n]*/gi),
        pattern2: html.match(/Today Answer.*:[^<\n]*/gi),
        pattern3: html.match(/Answer\s*:[^<\n]*/gi),
      },
      
      // 查找包含线索的部分
      cluesMatches: {
        pattern1: html.match(/Pinpoint Clues\s*:[^<]*(?:\n[^<\n]*){0,10}/gi),
        pattern2: html.match(/Clues\s*:[^<]*(?:\n[^<\n]*){0,10}/gi),
      },
      
      // 显示HTML的前1000个字符
      htmlPreview: html.substring(0, 1000),
      
      // 显示包含关键词的行
      relevantLines: html.split('\n')
        .map((line, index) => ({ line: line.trim(), number: index + 1 }))
        .filter(({ line }) => 
          line.toLowerCase().includes('pinpoint') ||
          line.toLowerCase().includes('answer') ||
          line.toLowerCase().includes('clue') ||
          line.toLowerCase().includes('game') ||
          /\d{4}-\d{1,2}-\d{1,2}/.test(line)
        )
        .slice(0, 20) // 只显示前20行相关内容
    };
    
    return respData({
      message: "调试信息获取成功",
      debug: debugInfo
    });

  } catch (error) {
    console.error('❌ 调试失败:', error);
    return respErr(`调试失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// GET方法用于健康检查
export async function GET() {
  return respData({
    status: "Pinpoint Debug API is running",
    timestamp: new Date().toISOString()
  });
}
