import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";

// Cloudflare Cron Trigger处理器
export async function POST(req: NextRequest) {
  try {
    // 验证请求来源（Cloudflare Cron会设置特定的headers）
    const cfCron = req.headers.get("cf-cron");
    const userAgent = req.headers.get("user-agent");
    
    // 验证是否来自Cloudflare Cron
    if (!cfCron && !userAgent?.includes("Cloudflare")) {
      return respErr("未授权的请求");
    }

    console.log('⏰ Cron任务触发 - 开始执行Pinpoint爬虫');
    
    // 获取当前域名
    const host = req.headers.get("host");
    const protocol = req.headers.get("x-forwarded-proto") || "https";
    const baseUrl = `${protocol}://${host}`;
    
    // 调用爬虫API
    const crawlerResponse = await fetch(`${baseUrl}/api/crawler/pinpoint`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRAWLER_SECRET}`
      }
    });

    const result = await crawlerResponse.json();

    if (crawlerResponse.ok) {
      console.log('✅ Cron任务执行成功:', result.message);
      return respData({
        success: true,
        message: "Cron任务执行成功",
        crawlerResult: result,
        timestamp: new Date().toISOString()
      });
    } else {
      console.error('❌ Cron任务执行失败:', result.message);
      return respErr(`Cron任务执行失败: ${result.message}`);
    }

  } catch (error) {
    console.error('❌ Cron任务异常:', error);
    return respErr(`Cron任务异常: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// GET方法用于健康检查
export async function GET() {
  return respData({
    status: "Pinpoint Cron API is running",
    nextRun: "每小时的第0分钟",
    timestamp: new Date().toISOString()
  });
}
