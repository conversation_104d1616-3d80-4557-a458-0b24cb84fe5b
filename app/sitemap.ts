import { MetadataRoute } from "next";
import { getAllPinpointAnswers } from "@/models/pinpoint";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://pinpointodays.com";
  
  try {
    // 获取所有已发布的答案
    const answers = await getAllPinpointAnswers();
    
    // 基础页面
    const staticPages: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 1,
      },
      {
        url: `${baseUrl}/history`,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 0.8,
      },
    ];

    // 答案详情页面
    const answerPages: MetadataRoute.Sitemap = answers.map((answer) => ({
      url: `${baseUrl}/${answer.url_slug}`,
      lastModified: new Date(answer.updated_at),
      changeFrequency: "weekly" as const,
      priority: 0.7,
    }));

    return [...staticPages, ...answerPages];
  } catch (error) {
    console.error("生成sitemap失败:", error);
    
    // 如果获取答案失败，至少返回基础页面
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 1,
      },
      {
        url: `${baseUrl}/history`,
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 0.8,
      },
    ];
  }
}
