:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.19 0.01 248.51);
  --card: oklch(0.98 0 197.14);
  --card-foreground: oklch(0.19 0.01 248.51);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.19 0.01 248.51);
  --primary: oklch(0.67 0.16 245);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.19 0.01 248.51);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.92 0 286.37);
  --muted-foreground: oklch(0.19 0.01 248.51);
  --accent: oklch(0.94 0.02 250.85);
  --accent-foreground: oklch(0.67 0.16 245);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.93 0.01 231.66);
  --input: oklch(0.98 0 228.78);
  --ring: oklch(0.68 0.16 243.35);
  --chart-1: oklch(0.67 0.16 245);
  --chart-2: oklch(0.69 0.16 160.35);
  --chart-3: oklch(0.82 0.16 82.53);
  --chart-4: oklch(0.71 0.18 151.71);
  --chart-5: oklch(0.59 0.22 10.58);
  --sidebar: oklch(0.98 0 197.14);
  --sidebar-foreground: oklch(0.19 0.01 248.51);
  --sidebar-primary: oklch(0.67 0.16 245);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.94 0.02 250.85);
  --sidebar-accent-foreground: oklch(0.67 0.16 245);
  --sidebar-border: oklch(0.93 0.01 238.52);
  --sidebar-ring: oklch(0.68 0.16 243.35);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
}

.dark {
  --background: oklch(0 0 0);
  --foreground: oklch(0.93 0 228.79);
  --card: oklch(0.21 0.01 274.53);
  --card-foreground: oklch(0.89 0 0);
  --popover: oklch(0 0 0);
  --popover-foreground: oklch(0.93 0 228.79);
  --primary: oklch(0.67 0.16 245.01);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.96 0 219.53);
  --secondary-foreground: oklch(0.19 0.01 248.51);
  --muted: oklch(0.21 0 0);
  --muted-foreground: oklch(0.56 0.01 247.97);
  --accent: oklch(0.19 0.03 242.55);
  --accent-foreground: oklch(0.67 0.16 245.01);
  --destructive: oklch(0.62 0.24 25.77);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.27 0 248);
  --input: oklch(0.3 0.03 244.82);
  --ring: oklch(0.68 0.16 243.35);
  --chart-1: oklch(0.67 0.16 245);
  --chart-2: oklch(0.69 0.16 160.35);
  --chart-3: oklch(0.82 0.16 82.53);
  --chart-4: oklch(0.71 0.18 151.71);
  --chart-5: oklch(0.59 0.22 10.58);
  --sidebar: oklch(0.21 0.01 274.53);
  --sidebar-foreground: oklch(0.89 0 0);
  --sidebar-primary: oklch(0.68 0.16 243.35);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.19 0.03 242.55);
  --sidebar-accent-foreground: oklch(0.67 0.16 245.01);
  --sidebar-border: oklch(0.38 0.02 240.59);
  --sidebar-ring: oklch(0.68 0.16 243.35);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
