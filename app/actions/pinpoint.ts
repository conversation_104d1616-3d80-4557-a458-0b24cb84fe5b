"use server";

import { redirect } from "next/navigation";
import { revalidateTag, revalidatePath } from "next/cache";
import { 
  insertPinpointAnswer, 
  updatePinpointAnswer as updateAnswer,
  findPinpointAnswerById,
  checkGameNumberExists,
  checkDateExists,
  checkUrlSlugExists
} from "@/models/pinpoint";
import { 
  validatePinpointData, 
  formatToDatabase,
  generateUrlSlug 
} from "@/lib/pinpoint";
import { CACHE_TAGS, getRevalidationPaths } from "@/lib/isr-config";

export async function createPinpointAnswer(formData: FormData) {
  try {
    const data = {
      gameNumber: parseInt(formData.get('gameNumber') as string),
      date: formData.get('date') as string,
      answer: (formData.get('answer') as string).trim(),
      clueWords: [
        (formData.get('clueWord1') as string).trim(),
        (formData.get('clueWord2') as string).trim(),
        (formData.get('clueWord3') as string).trim(),
        (formData.get('clueWord4') as string).trim(),
        (formData.get('clueWord5') as string).trim(),
      ] as [string, string, string, string, string],
    };

    // 验证数据格式
    const validation = validatePinpointData(data);
    if (!validation.isValid) {
      return {
        status: "error" as const,
        message: `数据验证失败: ${validation.errors.map(e => e.message).join(", ")}`,
      };
    }

    // 检查游戏编号是否已存在
    const gameNumberExists = await checkGameNumberExists(data.gameNumber);
    if (gameNumberExists) {
      return {
        status: "error" as const,
        message: `游戏编号 #${data.gameNumber} 已存在`,
      };
    }

    // 检查日期是否已存在
    const dateExists = await checkDateExists(data.date);
    if (dateExists) {
      return {
        status: "error" as const,
        message: `日期 ${data.date} 已存在答案`,
      };
    }

    // 生成URL slug并检查是否已存在
    const urlSlug = generateUrlSlug(data.gameNumber, data.clueWords);
    const slugExists = await checkUrlSlugExists(urlSlug);
    if (slugExists) {
      return {
        status: "error" as const,
        message: `URL slug 已存在，请检查游戏编号和线索词`,
      };
    }

    // 转换为数据库格式
    const pinpointAnswer = formatToDatabase(data);

    // 插入数据库
    const result = await insertPinpointAnswer(pinpointAnswer);

    // 触发相关页面重新生成
    try {
      revalidateTag(CACHE_TAGS.PINPOINT_ANSWERS);
      revalidateTag(CACHE_TAGS.TODAY_ANSWER);
      revalidateTag(CACHE_TAGS.RECENT_ANSWERS);
      revalidateTag(CACHE_TAGS.HISTORY_ANSWERS);

      const pathsToRevalidate = getRevalidationPaths('create');
      for (const path of pathsToRevalidate) {
        revalidatePath(path);
      }
      revalidatePath(`/${result.url_slug}`);
    } catch (revalidateError) {
      console.error("页面重新生成失败:", revalidateError);
    }

  } catch (error) {
    console.error("创建答案失败:", error);
    return {
      status: "error" as const,
      message: "创建答案失败",
    };
  }

  // 成功后跳转
  redirect("/admin/pinpoint");
}

export async function updatePinpointAnswer(answerId: number, formData: FormData) {
  try {
    const existingAnswer = await findPinpointAnswerById(answerId);
    if (!existingAnswer) {
      return {
        status: "error" as const,
        message: "答案不存在",
      };
    }

    const data = {
      gameNumber: parseInt(formData.get('gameNumber') as string),
      date: formData.get('date') as string,
      answer: (formData.get('answer') as string).trim(),
      clueWords: [
        (formData.get('clueWord1') as string).trim(),
        (formData.get('clueWord2') as string).trim(),
        (formData.get('clueWord3') as string).trim(),
        (formData.get('clueWord4') as string).trim(),
        (formData.get('clueWord5') as string).trim(),
      ] as [string, string, string, string, string],
      status: formData.get('status') as string,
    };

    const validation = validatePinpointData(data);
    if (!validation.isValid) {
      return {
        status: "error" as const,
        message: `数据验证失败: ${validation.errors.map(e => e.message).join(", ")}`,
      };
    }

    if (data.gameNumber !== existingAnswer.game_number) {
      const gameNumberExists = await checkGameNumberExists(data.gameNumber, answerId);
      if (gameNumberExists) {
        return {
          status: "error" as const,
          message: `游戏编号 #${data.gameNumber} 已存在`,
        };
      }
    }

    if (data.date !== existingAnswer.date) {
      const dateExists = await checkDateExists(data.date, answerId);
      if (dateExists) {
        return {
          status: "error" as const,
          message: `日期 ${data.date} 已存在答案`,
        };
      }
    }

    const newUrlSlug = generateUrlSlug(data.gameNumber, data.clueWords);
    if (newUrlSlug !== existingAnswer.url_slug) {
      const slugExists = await checkUrlSlugExists(newUrlSlug, answerId);
      if (slugExists) {
        return {
          status: "error" as const,
          message: `URL slug 已存在，请检查游戏编号和线索词`,
        };
      }
    }

    const updateData = {
      ...formatToDatabase(data),
      status: data.status as any,
    };

    const result = await updateAnswer(answerId, updateData);

    try {
      revalidateTag(CACHE_TAGS.PINPOINT_ANSWERS);
      revalidateTag(CACHE_TAGS.TODAY_ANSWER);
      revalidateTag(CACHE_TAGS.RECENT_ANSWERS);
      revalidateTag(CACHE_TAGS.HISTORY_ANSWERS);

      const pathsToRevalidate = getRevalidationPaths('update', result.url_slug);
      for (const path of pathsToRevalidate) {
        revalidatePath(path);
      }

      if (newUrlSlug !== existingAnswer.url_slug) {
        revalidatePath(`/${existingAnswer.url_slug}`);
      }
    } catch (revalidateError) {
      console.error("页面重新生成失败:", revalidateError);
    }

  } catch (error) {
    console.error("更新答案失败:", error);
    return {
      status: "error" as const,
      message: "更新答案失败",
    };
  }

  // 成功后跳转
  redirect("/admin/pinpoint");
}
