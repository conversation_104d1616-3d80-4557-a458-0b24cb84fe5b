import { Metadata } from "next";
import { notFound } from "next/navigation";
import { setRequestLocale } from "next-intl/server";
import { getAllPinpointAnswers, findPinpointAnswerBySlug } from "@/models/pinpoint";
import { formatPinpointAnswer, generateSEOData } from "@/lib/pinpoint";
import PinpointAnswerPage from "@/components/pinpoint/PinpointAnswerPage";
import JsonLd from "@/components/seo/JsonLd";

// 配置ISR - 每小时重新生成
export const revalidate = 3600;
export const dynamic = "force-static";
export const dynamicParams = true;

// 生成静态参数
export async function generateStaticParams({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  
  try {
    const answers = await getAllPinpointAnswers();
    return answers.map((answer) => ({
      slug: answer.url_slug,
    }));
  } catch (error) {
    console.error("生成静态参数失败:", error);
    return [];
  }
}

// 生成页面元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}): Promise<Metadata> {
  const { locale, slug } = await params;
  
  try {
    const answer = await findPinpointAnswerBySlug(slug);
    
    if (!answer) {
      return {
        title: "答案未找到 - LinkedIn Pinpoint",
        description: "抱歉，您查找的Pinpoint答案不存在。",
      };
    }

    const formattedAnswer = formatPinpointAnswer(answer);
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://pinpointodays.com";
    const seoData = generateSEOData(formattedAnswer, baseUrl);

    let canonicalUrl = `${baseUrl}/${slug}`;
    if (locale !== "en") {
      canonicalUrl = `${baseUrl}/${locale}/${slug}`;
    }

    return {
      title: seoData.title,
      description: seoData.description,
      keywords: seoData.keywords,
      openGraph: {
        title: seoData.title,
        description: seoData.description,
        url: canonicalUrl,
        siteName: "LinkedIn Pinpoint 每日答案",
        images: [
          {
            url: `${baseUrl}/og-image.png`,
            width: 1200,
            height: 630,
            alt: seoData.title,
          },
        ],
        locale: locale === "zh" ? "zh_CN" : "en_US",
        type: "article",
        publishedTime: answer.created_at,
        modifiedTime: answer.updated_at,
        authors: ["LinkedIn Pinpoint 每日答案"],
        tags: seoData.keywords,
      },
      twitter: {
        card: "summary_large_image",
        title: seoData.title,
        description: seoData.description,
        images: [`${baseUrl}/og-image.png`],
        creator: "@pinpointodays",
        site: "@pinpointodays",
      },
      alternates: {
        canonical: canonicalUrl,
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    };
  } catch (error) {
    console.error("生成元数据失败:", error);
    return {
      title: "LinkedIn Pinpoint 每日答案",
      description: "获取LinkedIn Pinpoint每日答案和解析。",
    };
  }
}

// 答案详情页面组件
export default async function AnswerDetailPage({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}) {
  const { locale, slug } = await params;
  setRequestLocale(locale);

  try {
    const answer = await findPinpointAnswerBySlug(slug);

    if (!answer) {
      notFound();
    }

    const formattedAnswer = formatPinpointAnswer(answer);
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "https://pinpointodays.com";

    // 生成JSON-LD结构化数据
    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "Article",
      headline: `Pinpoint #${formattedAnswer.gameNumber} 答案: ${formattedAnswer.answer}`,
      description: `LinkedIn Pinpoint 第${formattedAnswer.gameNumber}期答案揭晓！答案是"${formattedAnswer.answer}"，线索词包括：${formattedAnswer.clues.join('、')}。`,
      author: {
        "@type": "Organization",
        name: "LinkedIn Pinpoint 每日答案",
        url: baseUrl,
      },
      publisher: {
        "@type": "Organization",
        name: "LinkedIn Pinpoint 每日答案",
        url: baseUrl,
        logo: {
          "@type": "ImageObject",
          url: `${baseUrl}/logo.png`,
        },
      },
      datePublished: answer.created_at,
      dateModified: answer.updated_at,
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": `${baseUrl}/${locale === "en" ? "" : locale + "/"}${slug}`,
      },
      image: `${baseUrl}/og-image.png`,
      keywords: [
        "LinkedIn Pinpoint",
        "Pinpoint答案",
        `Pinpoint ${formattedAnswer.gameNumber}`,
        formattedAnswer.answer,
        ...formattedAnswer.clues,
      ].join(", "),
      articleSection: "游戏攻略",
      wordCount: 200,
      inLanguage: locale === "zh" ? "zh-CN" : "en-US",
    };

    return (
      <>
        <JsonLd data={jsonLd} />
        <PinpointAnswerPage answer={formattedAnswer} />
      </>
    );
  } catch (error) {
    console.error("加载答案详情失败:", error);
    notFound();
  }
}
