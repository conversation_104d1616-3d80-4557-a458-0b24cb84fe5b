import { setRequestLocale } from "next-intl/server";
import { getTodayPinpointAnswer, getRecentPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";
import TodayAnswerPreview from "@/components/pinpoint/TodayAnswerPreview";
import RecentAnswersList from "@/components/pinpoint/RecentAnswersList";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { History, Info, Github } from "lucide-react";

export const revalidate = 300; // 5分钟重新生成
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    title: "LinkedIn Pinpoint 每日答案 - 获取最新游戏答案和解析",
    description: "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。每日更新，助您轻松通关Pinpoint文字游戏。",
    keywords: [
      "LinkedIn Pinpoint",
      "Pinpoint答案",
      "每日答案",
      "游戏攻略",
      "文字游戏",
      "线索词",
      "LinkedIn游戏",
    ],
    openGraph: {
      title: "LinkedIn Pinpoint 每日答案",
      description: "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。",
      url: canonicalUrl,
      siteName: "LinkedIn Pinpoint 每日答案",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: "LinkedIn Pinpoint 每日答案",
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: "LinkedIn Pinpoint 每日答案",
      description: "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。",
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`],
      creator: "@pinpointodays",
      site: "@pinpointodays",
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function PinpointHomePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  try {
    // 获取今日答案和最近答案
    const [todayAnswer, recentAnswers] = await Promise.all([
      getTodayPinpointAnswer(),
      getRecentPinpointAnswers(10),
    ]);

    const formattedTodayAnswer = todayAnswer ? formatPinpointAnswer(todayAnswer) : null;
    const formattedRecentAnswers = recentAnswers.map(formatPinpointAnswer);

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-8">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              LinkedIn Pinpoint 每日答案
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              获取最新的Pinpoint游戏答案、线索词解析和历史答案查询
            </p>

            {/* 快速导航 */}
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/history">
                <Button variant="outline" className="flex items-center gap-2">
                  <History className="w-4 h-4" />
                  历史答案
                </Button>
              </Link>
              <Button variant="outline" className="flex items-center gap-2">
                <Info className="w-4 h-4" />
                游戏规则
              </Button>
              <Link href="https://github.com/your-repo" target="_blank">
                <Button variant="outline" className="flex items-center gap-2">
                  <Github className="w-4 h-4" />
                  开源项目
                </Button>
              </Link>
            </div>
          </div>

          {/* 主要内容 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {/* 今日答案预览 */}
            <div>
              <TodayAnswerPreview todayAnswer={formattedTodayAnswer} />
            </div>

            {/* 最近答案列表 */}
            <div>
              <RecentAnswersList recentAnswers={formattedRecentAnswers} />
            </div>
          </div>

          {/* 游戏介绍 */}
          <div className="mt-16 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                关于 LinkedIn Pinpoint
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-3">
                    🎯 游戏规则
                  </h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• 每天发布一个新的谜题</li>
                    <li>• 根据5个线索词找出共同点</li>
                    <li>• 答案通常是一个类别、主题或概念</li>
                    <li>• 每个谜题都有唯一的编号</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-3">
                    📱 如何使用
                  </h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• 查看今日答案和线索词</li>
                    <li>• 浏览历史答案和解析</li>
                    <li>• 分享答案给朋友</li>
                    <li>• 每日更新，不错过任何答案</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("加载首页数据失败:", error);

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            LinkedIn Pinpoint 每日答案
          </h1>
          <p className="text-gray-600 mb-8">
            抱歉，暂时无法加载数据，请稍后再试。
          </p>
          <Link href="/history">
            <Button>查看历史答案</Button>
          </Link>
        </div>
      </div>
    );
  }
}
