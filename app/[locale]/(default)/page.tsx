import { setRequestLocale } from "next-intl/server";
import { getTodayPinpointAnswer, getRecentPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";
import TodayAnswerPreview from "@/components/pinpoint/TodayAnswerPreview";
import RecentAnswersList from "@/components/pinpoint/RecentAnswersList";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { History, Info, Github } from "lucide-react";

export const revalidate = 300; // 5分钟重新生成
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    title: "LinkedIn Pinpoint 每日答案 - 获取最新游戏答案和解析",
    description: "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。每日更新，助您轻松通关Pinpoint文字游戏。",
    keywords: [
      "LinkedIn Pinpoint",
      "Pinpoint答案",
      "每日答案",
      "游戏攻略",
      "文字游戏",
      "线索词",
      "LinkedIn游戏",
    ],
    openGraph: {
      title: "LinkedIn Pinpoint 每日答案",
      description: "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。",
      url: canonicalUrl,
      siteName: "LinkedIn Pinpoint 每日答案",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: "LinkedIn Pinpoint 每日答案",
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: "LinkedIn Pinpoint 每日答案",
      description: "获取LinkedIn Pinpoint每日游戏答案、线索词解析和历史答案查询。",
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`],
      creator: "@pinpointodays",
      site: "@pinpointodays",
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function PinpointHomePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  try {
    // 获取今日答案和最近答案
    const [todayAnswer, recentAnswers] = await Promise.all([
      getTodayPinpointAnswer(),
      getRecentPinpointAnswers(10),
    ]);

    const formattedTodayAnswer = todayAnswer ? formatPinpointAnswer(todayAnswer) : null;
    const formattedRecentAnswers = recentAnswers.map(formatPinpointAnswer);

    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/30"></div>
          <div className="relative container mx-auto px-6 py-20 md:py-32">
            <div className="text-center max-w-4xl mx-auto fade-in">
              <h1 className="apple-title-hero mb-6">
                LinkedIn Pinpoint
                <br />
                <span style={{ color: 'var(--color-primary)' }}>每日答案</span>
              </h1>
              <p className="apple-body-large mb-12 max-w-2xl mx-auto">
                获取最新的Pinpoint游戏答案、线索词解析和历史答案查询。
                <br />
                每日更新，助您轻松通关文字游戏。
              </p>

              {/* 快速导航 */}
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/history" className="apple-button apple-button-primary">
                  <History className="w-5 h-5" />
                  历史答案
                </Link>
                <button className="apple-button apple-button-secondary">
                  <Info className="w-5 h-5" />
                  游戏规则
                </button>
                <Link
                  href="https://github.com/your-repo"
                  target="_blank"
                  className="apple-button apple-button-secondary"
                >
                  <Github className="w-5 h-5" />
                  开源项目
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="container mx-auto px-6 py-16">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-12 max-w-7xl mx-auto">
            {/* 今日答案预览 */}
            <div className="slide-up">
              <TodayAnswerPreview todayAnswer={formattedTodayAnswer} />
            </div>

            {/* 最近答案列表 */}
            <div className="slide-up" style={{ animationDelay: '150ms' }}>
              <RecentAnswersList recentAnswers={formattedRecentAnswers} />
            </div>
          </div>
        </section>

        {/* About Section */}
        <section className="container mx-auto px-6 py-16">
          <div className="max-w-5xl mx-auto">
            <div className="apple-card p-12">
              <h2 className="apple-title text-center mb-12">
                关于 LinkedIn Pinpoint
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl"
                         style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                      🎯
                    </div>
                    <div>
                      <h3 className="apple-subtitle mb-3">游戏规则</h3>
                      <ul className="space-y-3 apple-body">
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          每天发布一个新的谜题
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          根据5个线索词找出共同点
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          答案通常是一个类别、主题或概念
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          每个谜题都有唯一的编号
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl"
                         style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                      📱
                    </div>
                    <div>
                      <h3 className="apple-subtitle mb-3">如何使用</h3>
                      <ul className="space-y-3 apple-body">
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          查看今日答案和线索词
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          浏览历史答案和解析
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          分享答案给朋友
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          每日更新，不错过任何答案
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  } catch (error) {
    console.error("加载首页数据失败:", error);

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            LinkedIn Pinpoint 每日答案
          </h1>
          <p className="text-gray-600 mb-8">
            抱歉，暂时无法加载数据，请稍后再试。
          </p>
          <Link href="/history">
            <Button>查看历史答案</Button>
          </Link>
        </div>
      </div>
    );
  }
}
