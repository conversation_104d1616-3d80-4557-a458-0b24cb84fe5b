import { setRequestLocale, getTranslations } from "next-intl/server";
import { getTodayPinpointAnswer, getRecentPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";
import TodayAnswerPreview from "@/components/pinpoint/TodayAnswerPreview";
import RecentAnswersList from "@/components/pinpoint/RecentAnswersList";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { History, Info, Play } from "lucide-react";

export const revalidate = 300; // 5分钟重新生成
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations("homepage");

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    title: t("meta_title"),
    description: t("meta_description"),
    keywords: [
      "LinkedIn Pinpoint",
      "Pinpoint答案",
      "每日答案",
      "游戏攻略",
      "文字游戏",
      "线索词",
      "LinkedIn游戏",
    ],
    openGraph: {
      title: t("title"),
      description: t("meta_description"),
      url: canonicalUrl,
      siteName: t("title"),
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: t("title"),
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t("title"),
      description: t("meta_description"),
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`],
      creator: "@pinpointodays",
      site: "@pinpointodays",
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function PinpointHomePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const t = await getTranslations("homepage");

  try {
    // 获取今日答案和最近答案
    const [todayAnswer, recentAnswers] = await Promise.all([
      getTodayPinpointAnswer(),
      getRecentPinpointAnswers(10),
    ]);

    const formattedTodayAnswer = todayAnswer ? formatPinpointAnswer(todayAnswer) : null;
    const formattedRecentAnswers = recentAnswers.map(formatPinpointAnswer);

    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/30"></div>
          <div className="relative container mx-auto px-6 py-20 md:py-32">
            <div className="text-center max-w-4xl mx-auto fade-in">
              <h1 className="apple-title-hero mb-6">
                LinkedIn Pinpoint
                <br />
                <span style={{ color: 'var(--color-primary)' }}>{t("subtitle")}</span>
              </h1>
              <p className="apple-body-large mb-12 max-w-2xl mx-auto">
                {t("description")}
                <br />
                {t("description_line2")}
              </p>

              {/* 快速导航 */}
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/history" className="apple-button apple-button-primary">
                  <History className="w-5 h-5" />
                  {t("nav_history")}
                </Link>
                <Link href="#about-pinpoint" className="apple-button apple-button-secondary">
                  <Info className="w-5 h-5" />
                  {t("nav_rules")}
                </Link>
                <Link
                  href="https://www.linkedin.com/games/pinpoint/"
                  target="_blank"
                  className="apple-button apple-button-secondary"
                >
                  <Play className="w-5 h-5" />
                  {t("nav_play")}
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <section className="container mx-auto px-6 py-16">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-12 max-w-7xl mx-auto">
            {/* 今日答案预览 */}
            <div className="slide-up">
              <TodayAnswerPreview todayAnswer={formattedTodayAnswer} />
            </div>

            {/* 最近答案列表 */}
            <div className="slide-up" style={{ animationDelay: '150ms' }}>
              <RecentAnswersList recentAnswers={formattedRecentAnswers} />
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about-pinpoint" className="container mx-auto px-6 py-16">
          <div className="max-w-5xl mx-auto">
            <div className="apple-card p-12">
              <h2 className="apple-title text-center mb-12">
                {t("about_title")}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl"
                         style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                      🎯
                    </div>
                    <div>
                      <h3 className="apple-subtitle mb-3">{t("game_rules_title")}</h3>
                      <ul className="space-y-3 apple-body">
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("rule_1")}
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("rule_2")}
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("rule_3")}
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("rule_4")}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl"
                         style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                      📱
                    </div>
                    <div>
                      <h3 className="apple-subtitle mb-3">{t("how_to_use_title")}</h3>
                      <ul className="space-y-3 apple-body">
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("usage_1")}
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("usage_2")}
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("usage_3")}
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0"
                                style={{ backgroundColor: 'var(--color-primary)' }}></span>
                          {t("usage_4")}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  } catch (error) {
    console.error("加载首页数据失败:", error);

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {t("error_title")}
          </h1>
          <p className="text-gray-600 mb-8">
            {t("error_message")}
          </p>
          <Link href="/history">
            <Button>{t("error_button")}</Button>
          </Link>
        </div>
      </div>
    );
  }
}
