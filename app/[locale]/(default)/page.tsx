import { setRequestLocale } from "next-intl/server";
import { getTodayPinpointAnswer, getRecentPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";
import { getLandingPage } from "@/services/page";
import TodayAnswerPreview from "@/components/pinpoint/TodayAnswerPreview";
import RecentAnswersList from "@/components/pinpoint/RecentAnswersList";
import Hero from "@/components/blocks/hero";
import Branding from "@/components/blocks/branding";
import Feature from "@/components/blocks/feature";
import Stats from "@/components/blocks/stats";
import FAQ from "@/components/blocks/faq";
import CTA from "@/components/blocks/cta";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export const revalidate = 300; // 5分钟重新生成
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const landingPage = await getLandingPage(locale);

  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  // 使用 landing 配置中的 hero 内容作为 meta 信息
  const title = landingPage.hero?.title || "LinkedIn Pinpoint Daily Answers";
  const description = landingPage.hero?.description?.replace(/<br\/?>/g, ' ') || "Get LinkedIn Pinpoint daily answers and solutions";

  return {
    title,
    description,
    keywords: [
      "LinkedIn Pinpoint",
      "Pinpoint Answers",
      "Pinpoint Daily Answers",
    ],
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      siteName: title,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`],
      creator: "@pinpointodays",
      site: "@pinpointodays",
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function PinpointHomePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  try {
    // 获取 landing page 配置和数据
    const [landingPage, todayAnswer, recentAnswers] = await Promise.all([
      getLandingPage(locale),
      getTodayPinpointAnswer(),
      getRecentPinpointAnswers(6), // 上下排列可以显示更多项目
    ]);

    // 如果没有今日答案，使用最新的答案作为今日答案
    const displayTodayAnswer = todayAnswer || (recentAnswers.length > 0 ? recentAnswers[0] : null);

    const formattedTodayAnswer = displayTodayAnswer ? formatPinpointAnswer(displayTodayAnswer) : null;
    const formattedRecentAnswers = recentAnswers.map(formatPinpointAnswer);

    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        {/* Hero Section - 使用 landing 配置 */}
        {landingPage.hero && <Hero hero={landingPage.hero} />}

        {/* Branding Section - 使用 landing 配置 */}
        {/* {landingPage.branding && <Branding section={landingPage.branding} />} */}

        {/* Today's Answer Section - 保留核心功能 */}
        <section id="today-answer" className="container mx-auto px-6 py-16">
          <div className="max-w-4xl mx-auto space-y-12">
            {/* 今日答案预览 */}
            <div className="slide-up">
              <TodayAnswerPreview todayAnswer={formattedTodayAnswer} />
            </div>

            {/* 最近答案列表 */}
            <div className="slide-up" style={{ animationDelay: '150ms' }}>
              <RecentAnswersList recentAnswers={formattedRecentAnswers} />
            </div>
          </div>
        </section>

        {/* Feature Section - 使用 landing 配置 */}
        {landingPage.feature && <Feature section={landingPage.feature} />}

        {/* Stats Section - 使用 landing 配置 */}
        {landingPage.stats && <Stats section={landingPage.stats} />}

        {/* FAQ Section - 使用 landing 配置 */}
        {landingPage.faq && <FAQ section={landingPage.faq} />}

        {/* CTA Section - 使用 landing 配置 */}
        {landingPage.cta && <CTA section={landingPage.cta} />}
      </div>
    );
  } catch (error) {
    console.error("加载首页数据失败:", error);

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {locale === "zh" ? "加载失败" : "Loading Failed"}
          </h1>
          <p className="text-gray-600 mb-8">
            {locale === "zh" ? "无法加载页面数据，请稍后重试。" : "Unable to load page data, please try again later."}
          </p>
          <Link href="/history">
            <Button>{locale === "zh" ? "查看历史" : "View History"}</Button>
          </Link>
        </div>
      </div>
    );
  }
}
