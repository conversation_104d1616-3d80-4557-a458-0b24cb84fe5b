import { Metadata } from "next";
import { setRequestLocale } from "next-intl/server";
import { getAllPinpointAnswers } from "@/models/pinpoint";
import { formatPinpointAnswer } from "@/lib/pinpoint";
import HistoryAnswersPage from "@/components/pinpoint/HistoryAnswersPage";

// 配置ISR - 每小时重新生成
export const revalidate = 3600;
export const dynamic = "force-static";

// 生成页面元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/history`;
  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/history`;
  }

  return {
    title: "历史答案 - LinkedIn Pinpoint 每日答案",
    description: "浏览LinkedIn Pinpoint所有历史答案，包括游戏编号、日期、答案和线索词。快速查找您需要的Pinpoint答案。",
    keywords: [
      "LinkedIn Pinpoint",
      "历史答案",
      "Pinpoint答案查询",
      "游戏攻略",
      "文字游戏",
      "线索词",
      "答案列表",
    ],
    openGraph: {
      title: "历史答案 - LinkedIn Pinpoint 每日答案",
      description: "浏览LinkedIn Pinpoint所有历史答案，包括游戏编号、日期、答案和线索词。",
      url: canonicalUrl,
      siteName: "LinkedIn Pinpoint 每日答案",
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`,
          width: 1200,
          height: 630,
          alt: "LinkedIn Pinpoint 历史答案",
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: "历史答案 - LinkedIn Pinpoint 每日答案",
      description: "浏览LinkedIn Pinpoint所有历史答案，包括游戏编号、日期、答案和线索词。",
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/og-image.png`],
      creator: "@pinpointodays",
      site: "@pinpointodays",
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

// 历史答案页面组件
export default async function HistoryPage({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ page?: string; search?: string }>;
}) {
  const { locale } = await params;
  const { page = "1", search = "" } = await searchParams;
  
  setRequestLocale(locale);

  try {
    // 获取所有答案
    const allAnswers = await getAllPinpointAnswers();
    const formattedAnswers = allAnswers.map(formatPinpointAnswer);

    // 搜索过滤
    let filteredAnswers = formattedAnswers;
    if (search) {
      const searchLower = search.toLowerCase();
      filteredAnswers = formattedAnswers.filter(
        (answer) =>
          answer.answer.toLowerCase().includes(searchLower) ||
          answer.clues.some((clue) => clue.toLowerCase().includes(searchLower)) ||
          answer.gameNumber.toString().includes(searchLower)
      );
    }

    // 分页配置
    const pageSize = 20;
    const currentPage = Math.max(1, parseInt(page));
    const totalPages = Math.ceil(filteredAnswers.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedAnswers = filteredAnswers.slice(startIndex, endIndex);

    return (
      <HistoryAnswersPage
        answers={paginatedAnswers}
        currentPage={currentPage}
        totalPages={totalPages}
        totalAnswers={filteredAnswers.length}
        searchQuery={search}
      />
    );
  } catch (error) {
    console.error("加载历史答案失败:", error);
    
    return (
      <HistoryAnswersPage
        answers={[]}
        currentPage={1}
        totalPages={0}
        totalAnswers={0}
        searchQuery={search}
        error="加载历史答案失败，请稍后再试。"
      />
    );
  }
}
