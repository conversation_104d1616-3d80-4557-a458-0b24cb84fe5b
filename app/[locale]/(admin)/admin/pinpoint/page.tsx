import Dropdown from "@/components/blocks/table/dropdown";
import { NavItem } from "@/types/blocks/base";
import { PinpointAnswer } from "@/types/pinpoint";
import TableSlot from "@/components/dashboard/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getPinpointAnswersForAdmin } from "@/models/pinpoint";
import moment from "moment";

export default async function PinpointAdminPage() {
  // 获取答案列表（默认显示前20条）
  const result = await getPinpointAnswersForAdmin({
    page: 1,
    limit: 20,
    status: 'all'
  });

  const answers = result.data;

  const table: TableSlotType = {
    title: "Pinpoint 答案管理",
    toolbar: {
      items: [
        {
          title: "新增答案",
          icon: "RiAddLine",
          url: "/admin/pinpoint/new",
        },
        {
          title: "查看网站",
          icon: "RiExternalLinkLine",
          url: "/",
          target: "_blank",
        },
      ],
    },
    columns: [
      {
        name: "game_number",
        title: "游戏编号",
        callback: (item: PinpointAnswer) => {
          return `#${item.game_number}`;
        },
      },
      {
        name: "date",
        title: "日期",
        callback: (item: PinpointAnswer) => {
          return moment(item.date).format("YYYY-MM-DD");
        },
      },
      {
        name: "answer",
        title: "答案",
        callback: (item: PinpointAnswer) => {
          // 限制显示长度
          return item.answer.length > 30 
            ? `${item.answer.substring(0, 30)}...` 
            : item.answer;
        },
      },
      {
        name: "clues",
        title: "线索词",
        callback: (item: PinpointAnswer) => {
          const clues = [
            item.clue_word_1,
            item.clue_word_2,
            item.clue_word_3,
            item.clue_word_4,
            item.clue_word_5,
          ];
          return clues.join(", ");
        },
      },
      {
        name: "status",
        title: "状态",
        callback: (item: PinpointAnswer) => {
          const statusMap = {
            published: "已发布",
            draft: "草稿",
            deleted: "已删除",
          };
          const status = item.status || 'published';
          const statusText = statusMap[status] || status;
          
          // 添加状态颜色
          const colorMap = {
            published: "text-green-600",
            draft: "text-yellow-600", 
            deleted: "text-red-600",
          };
          const colorClass = colorMap[status] || "text-gray-600";
          
          return <span className={colorClass}>{statusText}</span>;
        },
      },
      {
        name: "created_at",
        title: "创建时间",
        callback: (item: PinpointAnswer) => {
          return moment(item.created_at).format("MM-DD HH:mm");
        },
      },
      {
        callback: (item: PinpointAnswer) => {
          const items: NavItem[] = [
            {
              title: "编辑",
              icon: "RiEditLine",
              url: `/admin/pinpoint/${item.id}/edit`,
            },
            {
              title: "查看",
              icon: "RiEyeLine",
              url: `/${item.url_slug}`,
              target: "_blank",
            },
          ];

          // 只有已发布的答案才显示复制链接
          if (item.status === 'published') {
            items.push({
              title: "复制链接",
              icon: "RiLinkLine",
              onClick: () => {
                navigator.clipboard.writeText(`${window.location.origin}/${item.url_slug}`);
              },
            });
          }

          // 软删除功能
          if (item.status !== 'deleted') {
            items.push({
              title: "删除",
              icon: "RiDeleteBinLine",
              onClick: async () => {
                if (confirm(`确定要删除游戏 #${item.game_number} 的答案吗？`)) {
                  try {
                    const response = await fetch(`/api/admin/pinpoint/${item.id}`, {
                      method: 'DELETE',
                    });
                    
                    if (response.ok) {
                      window.location.reload();
                    } else {
                      const error = await response.json();
                      alert(`删除失败: ${error.message}`);
                    }
                  } catch (error) {
                    alert('删除失败，请重试');
                  }
                }
              },
            });
          }

          return <Dropdown items={items} />;
        },
      },
    ],
    data: answers,
    empty_message: "暂无答案数据",
  };

  return (
    <div className="space-y-6">
      <TableSlot {...table} />
      
      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-500">总答案数</div>
          <div className="text-2xl font-bold">{result.pagination.total}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-500">已发布</div>
          <div className="text-2xl font-bold text-green-600">
            {answers.filter(a => a.status === 'published').length}
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-500">草稿</div>
          <div className="text-2xl font-bold text-yellow-600">
            {answers.filter(a => a.status === 'draft').length}
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-sm text-gray-500">已删除</div>
          <div className="text-2xl font-bold text-red-600">
            {answers.filter(a => a.status === 'deleted').length}
          </div>
        </div>
      </div>

      {/* 快速操作提示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-2">💡 快速操作提示</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• 点击"新增答案"创建今日或未来的答案</li>
          <li>• 编辑答案后会自动重新生成相关页面</li>
          <li>• 删除答案为软删除，不会真正删除数据</li>
          <li>• 复制链接可以快速分享答案页面</li>
        </ul>
      </div>
    </div>
  );
}
