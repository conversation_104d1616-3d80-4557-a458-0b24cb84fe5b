import Empty from "@/components/blocks/empty";
import FormSlot from "@/components/dashboard/slots/form";
import { Form as FormSlotType } from "@/types/slots/form";
import { getUserInfo } from "@/services/user";
import { generateUrlSlug } from "@/lib/pinpoint";
import { createPinpointAnswer } from "@/app/actions/pinpoint";

export default async function NewPinpointPage() {
  // 临时注释登录校验，方便测试
  // const user = await getUserInfo();
  // if (!user || !user.uuid) {
  //   return <Empty message="未授权访问" />;
  // }

  // 获取今天的日期作为默认值
  const today = new Date().toISOString().split('T')[0];

  const form: FormSlotType = {
    title: "新增 Pinpoint 答案",
    crumb: {
      items: [
        {
          title: "Pinpoint",
          url: "/admin/pinpoint",
        },
        {
          title: "新增答案",
          is_active: true,
        },
      ],
    },
    fields: [
      {
        name: "game<PERSON><PERSON><PERSON>",
        title: "游戏编号",
        type: "number",
        placeholder: "458",
        validation: {
          required: true,
          min: 1,
        },
        tip: "LinkedIn Pinpoint 的游戏编号，必须是正整数且唯一",
      },
      {
        name: "date",
        title: "日期",
        type: "date",
        default_value: today,
        validation: {
          required: true,
        },
        tip: "答案对应的日期，格式：YYYY-MM-DD",
      },
      {
        name: "answer",
        title: "答案",
        type: "text",
        placeholder: "Words that come after 'head'",
        validation: {
          required: true,
          maxLength: 255,
        },
        tip: "Pinpoint 游戏的正确答案或类别描述",
      },
      {
        name: "clueWord1",
        title: "线索词 1",
        type: "text",
        placeholder: "book",
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord2",
        title: "线索词 2",
        type: "text",
        placeholder: "point",
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord3",
        title: "线索词 3",
        type: "text",
        placeholder: "list",
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord4",
        title: "线索词 4",
        type: "text",
        placeholder: "mate",
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord5",
        title: "线索词 5",
        type: "text",
        placeholder: "please",
        validation: {
          required: true,
          maxLength: 100,
        },
      },
    ],
    submit: {
      button: {
        title: "创建答案",
      },
      action: createPinpointAnswer,
    },
  };

  return (
    <div className="space-y-6">
      <FormSlot {...form} />
      
      {/* URL 预览组件 */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-800 mb-2">📝 URL 预览</h3>
        <p className="text-sm text-gray-600 mb-2">
          根据游戏编号和线索词，系统将自动生成以下URL：
        </p>
        <div className="bg-white border rounded p-2 font-mono text-sm text-blue-600">
          <span id="url-preview">linkedin-pinpoint-[游戏编号]-[线索词1]-[线索词2]-[线索词3]-[线索词4]-[线索词5]</span>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          * URL会自动处理特殊字符和空格
        </p>
      </div>

      {/* 操作提示 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-2">💡 填写提示</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• <strong>游戏编号</strong>：必须是唯一的正整数，建议按时间顺序递增</li>
          <li>• <strong>日期</strong>：选择答案对应的日期，通常是游戏发布日期</li>
          <li>• <strong>答案</strong>：简洁明了地描述答案类别或主题</li>
          <li>• <strong>线索词</strong>：按照游戏中出现的顺序填写5个线索词</li>
          <li>• 创建后会自动生成对应的网页，并触发相关页面更新</li>
        </ul>
      </div>


    </div>
  );
}
