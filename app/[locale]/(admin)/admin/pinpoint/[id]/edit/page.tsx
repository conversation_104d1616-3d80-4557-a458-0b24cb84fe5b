import Empty from "@/components/blocks/empty";
import FormSlot from "@/components/dashboard/slots/form";
import { Form as FormSlotType } from "@/types/slots/form";
import { getUserInfo } from "@/services/user";
import { findPinpointAnswerById } from "@/models/pinpoint";
import { updatePinpointAnswer } from "@/app/actions/pinpoint";
import DeleteButton from "@/components/admin/pinpoint/DeleteButton";

export default async function EditPinpointPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  // 临时注释登录校验，方便测试
  // const user = await getUserInfo();
  // if (!user || !user.uuid) {
  //   return <Empty message="未授权访问" />;
  // }

  const answerId = parseInt(id);
  if (isNaN(answerId)) {
    return <Empty message="无效的答案ID" />;
  }

  const answer = await findPinpointAnswerById(answerId);
  if (!answer || !answer.id) {
    return <Empty message="答案不存在" />;
  }

  const form: FormSlotType = {
    title: `编辑 Pinpoint #${answer.game_number}`,
    crumb: {
      items: [
        {
          title: "Pinpoint",
          url: "/admin/pinpoint",
        },
        {
          title: `编辑 #${answer.game_number}`,
          is_active: true,
        },
      ],
    },
    fields: [
      {
        name: "gameNumber",
        title: "游戏编号",
        type: "number",
        default_value: answer.game_number.toString(),
        validation: {
          required: true,
          min: 1,
        },
        tip: "LinkedIn Pinpoint 的游戏编号，必须是正整数且唯一",
      },
      {
        name: "date",
        title: "日期",
        type: "date",
        default_value: answer.date,
        validation: {
          required: true,
        },
        tip: "答案对应的日期，格式：YYYY-MM-DD",
      },
      {
        name: "answer",
        title: "答案",
        type: "text",
        default_value: answer.answer,
        validation: {
          required: true,
          maxLength: 255,
        },
        tip: "Pinpoint 游戏的正确答案或类别描述",
      },
      {
        name: "clueWord1",
        title: "线索词 1",
        type: "text",
        default_value: answer.clue_word_1,
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord2",
        title: "线索词 2",
        type: "text",
        default_value: answer.clue_word_2,
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord3",
        title: "线索词 3",
        type: "text",
        default_value: answer.clue_word_3,
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord4",
        title: "线索词 4",
        type: "text",
        default_value: answer.clue_word_4,
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "clueWord5",
        title: "线索词 5",
        type: "text",
        default_value: answer.clue_word_5,
        validation: {
          required: true,
          maxLength: 100,
        },
      },
      {
        name: "status",
        title: "状态",
        type: "select",
        default_value: answer.status || 'published',
        options: [
          { value: "published", title: "已发布" },
          { value: "draft", title: "草稿" },
        ],
        validation: {
          required: true,
        },
        tip: "答案的发布状态",
      },
    ],
    submit: {
      button: {
        title: "更新答案",
      },
      action: updatePinpointAnswer.bind(null, answer.id),
    },
  };

  return (
    <div className="space-y-6">
      <FormSlot {...form} />
      
      {/* 当前URL显示 */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-800 mb-2">🔗 当前URL</h3>
        <div className="bg-white border rounded p-2 font-mono text-sm">
          <a 
            href={`/${answer.url_slug}`} 
            target="_blank" 
            className="text-blue-600 hover:text-blue-800"
          >
            /{answer.url_slug}
          </a>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          * 修改游戏编号或线索词会自动生成新的URL
        </p>
      </div>

      {/* 答案信息 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-800 mb-3">📊 答案信息</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">创建时间：</span>
            <span>{new Date(answer.created_at!).toLocaleString('zh-CN')}</span>
          </div>
          <div>
            <span className="text-gray-500">更新时间：</span>
            <span>{new Date(answer.updated_at!).toLocaleString('zh-CN')}</span>
          </div>
          <div>
            <span className="text-gray-500">当前状态：</span>
            <span className={
              answer.status === 'published' ? 'text-green-600' :
              answer.status === 'draft' ? 'text-yellow-600' : 'text-red-600'
            }>
              {answer.status === 'published' ? '已发布' :
               answer.status === 'draft' ? '草稿' : '已删除'}
            </span>
          </div>
          <div>
            <span className="text-gray-500">数据库ID：</span>
            <span>{answer.id}</span>
          </div>
        </div>
      </div>

      {/* 操作提示 */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-yellow-800 mb-2">⚠️ 编辑提示</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• <strong>游戏编号</strong>：修改后必须确保唯一性，建议谨慎修改</li>
          <li>• <strong>线索词</strong>：修改后会生成新的URL，旧URL将失效</li>
          <li>• <strong>状态</strong>：只有"已发布"状态的答案才会在前端显示</li>
          <li>• 保存后会自动重新生成相关页面，可能需要几分钟生效</li>
          <li>• 建议在非高峰时段进行重要修改</li>
        </ul>
      </div>

      {/* 危险操作区域 */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-red-800 mb-2">🚨 危险操作</h3>
        <p className="text-sm text-red-700 mb-3">
          以下操作不可逆，请谨慎操作：
        </p>
        <DeleteButton
          answerId={answer.id}
          gameNumber={answer.game_number}
        />
      </div>
    </div>
  );
}
