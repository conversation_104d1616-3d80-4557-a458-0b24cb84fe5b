/* Apple Design System - CSS Variables */
:root {
  /* Colors - Apple Style */
  --apple-blue: #007AFF;
  --apple-blue-light: #5AC8FA;
  --apple-blue-dark: #0051D5;
  
  --apple-gray-1: #FBFBFD;
  --apple-gray-2: #F5F5F7;
  --apple-gray-3: #E5E5EA;
  --apple-gray-4: #D2D2D7;
  --apple-gray-5: #AEAEB2;
  --apple-gray-6: #86868B;
  --apple-gray-7: #48484A;
  --apple-gray-8: #1D1D1F;
  
  --apple-white: #FFFFFF;
  --apple-black: #000000;
  
  /* Semantic Colors */
  --color-primary: var(--apple-blue);
  --color-primary-light: var(--apple-blue-light);
  --color-primary-dark: var(--apple-blue-dark);
  
  --color-background: var(--apple-gray-1);
  --color-surface: var(--apple-white);
  --color-surface-secondary: var(--apple-gray-2);
  
  --color-text-primary: var(--apple-gray-8);
  --color-text-secondary: var(--apple-gray-6);
  --color-text-tertiary: var(--apple-gray-5);
  
  --color-border: var(--apple-gray-4);
  --color-border-light: var(--apple-gray-3);
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.08);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.10);
  
  /* Border Radius */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  
  /* Spacing */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  
  /* Typography */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  --font-size-5xl: 48px;
  --font-size-6xl: 60px;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;
  
  /* Transitions */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
}

/* Apple-style base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Apple-style button */
.apple-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.apple-button-primary {
  background-color: var(--color-primary);
  color: var(--apple-white);
}

.apple-button-primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.apple-button-secondary {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.apple-button-secondary:hover {
  background-color: var(--color-surface-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Apple-style card */
.apple-card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-light);
}

.apple-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Apple-style input */
.apple-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background-color: var(--color-surface);
  transition: all var(--transition-fast);
}

.apple-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* Typography classes */
.apple-title-hero {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

.apple-title-large {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

.apple-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

.apple-subtitle {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
}

.apple-body-large {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-secondary);
}

.apple-body {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-secondary);
}

.apple-caption {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-text-tertiary);
}

/* Animation utilities */
.fade-in {
  animation: fadeIn var(--transition-slow) ease-out;
}

.slide-up {
  animation: slideUp var(--transition-slow) ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .apple-title-hero {
    font-size: var(--font-size-4xl);
  }
  
  .apple-title-large {
    font-size: var(--font-size-3xl);
  }
  
  .apple-title {
    font-size: var(--font-size-2xl);
  }
}
