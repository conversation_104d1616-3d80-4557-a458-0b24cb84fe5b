"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { PinpointFormData, PinpointAnswer } from "@/types/pinpoint";
import { 
  usePinpointFormValidation, 
  submitPinpointForm,
  FormErrorAlert,
  FormSuccessAlert 
} from "./PinpointFormValidation";

interface PinpointFormProps {
  initialData?: PinpointAnswer;
  mode: 'create' | 'edit';
  onSuccess?: () => void;
}

export default function PinpointForm({ initialData, mode, onSuccess }: PinpointFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string>("");
  const [submitSuccess, setSubmitSuccess] = useState<string>("");

  // 初始化表单数据
  const initialFormData: Partial<PinpointFormData> = initialData ? {
    gameNumber: initialData.game_number,
    date: initialData.date,
    answer: initialData.answer,
    clueWords: [
      initialData.clue_word_1,
      initialData.clue_word_2,
      initialData.clue_word_3,
      initialData.clue_word_4,
      initialData.clue_word_5,
    ],
    status: initialData.status,
  } : {
    date: new Date().toISOString().split('T')[0], // 默认今天
    clueWords: ['', '', '', '', ''],
    status: 'published',
  };

  const {
    formData,
    isValid,
    errors,
    urlPreview,
    updateField,
    updateClueWord,
    handleValidationChange,
    handleUrlPreviewChange,
    setFormData,
  } = usePinpointFormValidation(initialFormData);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isValid) {
      setSubmitError("请先修正表单错误");
      return;
    }

    setIsSubmitting(true);
    setSubmitError("");
    setSubmitSuccess("");

    try {
      const apiUrl = mode === 'create' 
        ? '/api/admin/pinpoint'
        : `/api/admin/pinpoint/${initialData?.id}`;
      
      const method = mode === 'create' ? 'POST' : 'PUT';
      
      const result = await submitPinpointForm(
        formData as PinpointFormData,
        apiUrl,
        method
      );

      if (result.success) {
        const successMessage = mode === 'create' 
          ? "答案创建成功！相关页面正在重新生成..." 
          : "答案更新成功！相关页面正在重新生成...";
        
        setSubmitSuccess(successMessage);
        toast.success(successMessage);
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          if (onSuccess) {
            onSuccess();
          } else {
            router.push('/admin/pinpoint');
          }
        }, 2000);
      } else {
        setSubmitError(result.error || "提交失败");
        toast.error(result.error || "提交失败");
      }
    } catch (error) {
      const errorMessage = "网络错误，请重试";
      setSubmitError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? '新增 Pinpoint 答案' : `编辑 Pinpoint #${initialData?.game_number}`}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 错误和成功提示 */}
          {submitError && <FormErrorAlert errors={[submitError]} />}
          {submitSuccess && <FormSuccessAlert message={submitSuccess} />}
          
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="gameNumber">游戏编号 *</Label>
              <Input
                id="gameNumber"
                type="number"
                min="1"
                value={formData.gameNumber || ''}
                onChange={(e) => updateField('gameNumber', parseInt(e.target.value) || 0)}
                placeholder="458"
                disabled={isSubmitting}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="date">日期 *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date || ''}
                onChange={(e) => updateField('date', e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* 答案 */}
          <div className="space-y-2">
            <Label htmlFor="answer">答案 *</Label>
            <Input
              id="answer"
              value={formData.answer || ''}
              onChange={(e) => updateField('answer', e.target.value)}
              placeholder="Words that come after 'head'"
              disabled={isSubmitting}
            />
          </div>

          {/* 线索词 */}
          <div className="space-y-4">
            <Label>线索词 *</Label>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {[0, 1, 2, 3, 4].map((index) => (
                <div key={index} className="space-y-2">
                  <Label htmlFor={`clue${index + 1}`} className="text-sm">
                    线索词 {index + 1}
                  </Label>
                  <Input
                    id={`clue${index + 1}`}
                    value={formData.clueWords?.[index] || ''}
                    onChange={(e) => updateClueWord(index, e.target.value)}
                    placeholder={['book', 'point', 'list', 'mate', 'please'][index]}
                    disabled={isSubmitting}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* 状态选择（仅编辑模式） */}
          {mode === 'edit' && (
            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <Select
                value={formData.status || 'published'}
                onValueChange={(value) => updateField('status', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="published">已发布</SelectItem>
                  <SelectItem value="draft">草稿</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* URL预览 */}
          {urlPreview && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <Label className="text-sm font-medium text-blue-800">URL预览</Label>
              <div className="mt-2 bg-white border rounded p-2 font-mono text-sm text-blue-600">
                /{urlPreview}
              </div>
              {mode === 'edit' && initialData && urlPreview !== initialData.url_slug && (
                <p className="text-xs text-orange-600 mt-2">
                  ⚠️ URL将从 /{initialData.url_slug} 更改为 /{urlPreview}
                </p>
              )}
            </div>
          )}

          {/* 表单验证状态 */}
          <div className={`border rounded-lg p-3 ${
            isValid 
              ? 'bg-green-50 border-green-200' 
              : 'bg-yellow-50 border-yellow-200'
          }`}>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                isValid ? 'bg-green-500' : 'bg-yellow-500'
              }`} />
              <span className={`text-sm font-medium ${
                isValid ? 'text-green-800' : 'text-yellow-800'
              }`}>
                {isValid ? '表单验证通过' : '表单需要完善'}
              </span>
            </div>
            {errors.length > 0 && (
              <ul className="mt-2 text-sm text-yellow-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            )}
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/pinpoint')}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={!isValid || isSubmitting}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === 'create' ? '创建答案' : '更新答案'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
