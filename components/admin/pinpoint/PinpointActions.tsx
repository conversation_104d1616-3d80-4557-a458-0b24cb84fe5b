"use client";

import { useState } from "react";
import Dropdown from "@/components/blocks/table/dropdown";
import { NavItem } from "@/types/blocks/base";
import { PinpointAnswer } from "@/types/pinpoint";
import { toast } from "sonner";

interface PinpointActionsProps {
  answer: PinpointAnswer;
  onDelete?: () => void;
}

export default function PinpointActions({ answer, onDelete }: PinpointActionsProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (isDeleting) return;
    
    if (!confirm(`确定要删除游戏 #${answer.game_number} 的答案吗？`)) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/admin/pinpoint/${answer.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        toast.success('答案已删除');
        if (onDelete) {
          onDelete();
        } else {
          // 刷新页面
          window.location.reload();
        }
      } else {
        const error = await response.json();
        toast.error(`删除失败: ${error.message}`);
      }
    } catch (error) {
      toast.error('删除失败，请重试');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCopyLink = () => {
    const url = `${window.location.origin}/${answer.url_slug}`;
    navigator.clipboard.writeText(url).then(() => {
      toast.success('链接已复制到剪贴板');
    }).catch(() => {
      toast.error('复制失败');
    });
  };

  const items: NavItem[] = [
    {
      title: "编辑",
      icon: "RiEditLine",
      url: `/admin/pinpoint/${answer.id}/edit`,
    },
    {
      title: "查看",
      icon: "RiEyeLine",
      url: `/${answer.url_slug}`,
      target: "_blank",
    },
  ];

  // 只有已发布的答案才显示复制链接
  if (answer.status === 'published') {
    items.push({
      title: "复制链接",
      icon: "RiLinkLine",
      onClick: handleCopyLink,
    });
  }

  // 软删除功能
  if (answer.status !== 'deleted') {
    items.push({
      title: isDeleting ? "删除中..." : "删除",
      icon: "RiDeleteBinLine",
      onClick: isDeleting ? undefined : handleDelete,
    });
  }

  return <Dropdown items={items} />;
}
