"use client";

import { useState, useEffect } from "react";
import { PinpointFormData, ValidationResult } from "@/types/pinpoint";
import { validatePinpointData, generateUrlSlug } from "@/lib/pinpoint";

interface PinpointFormValidationProps {
  formData: Partial<PinpointFormData>;
  onValidationChange: (isValid: boolean, errors: string[]) => void;
  onUrlPreviewChange: (urlSlug: string) => void;
}

export default function PinpointFormValidation({
  formData,
  onValidationChange,
  onUrlPreviewChange,
}: PinpointFormValidationProps) {
  const [validation, setValidation] = useState<ValidationResult>({
    isValid: true,
    errors: [],
  });
  const [urlPreview, setUrlPreview] = useState<string>("");

  useEffect(() => {
    // 验证表单数据
    const validationResult = validatePinpointData(formData);
    setValidation(validationResult);
    
    // 生成URL预览
    if (formData.gameNumber && formData.clueWords && formData.clueWords.length === 5) {
      const slug = generateUrlSlug(formData.gameNumber, formData.clueWords);
      setUrlPreview(slug);
      onUrlPreviewChange(slug);
    } else {
      setUrlPreview("");
      onUrlPreviewChange("");
    }

    // 通知父组件验证结果
    onValidationChange(
      validationResult.isValid,
      validationResult.errors.map(e => e.message)
    );
  }, [formData, onValidationChange, onUrlPreviewChange]);

  if (validation.isValid && !urlPreview) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* 验证错误显示 */}
      {!validation.isValid && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-red-800 mb-2">❌ 表单验证错误</h3>
          <ul className="text-sm text-red-700 space-y-1">
            {validation.errors.map((error, index) => (
              <li key={index}>• {error.message}</li>
            ))}
          </ul>
        </div>
      )}

      {/* URL预览 */}
      {urlPreview && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">🔗 URL预览</h3>
          <div className="bg-white border rounded p-2 font-mono text-sm text-blue-600">
            {urlPreview}
          </div>
          <p className="text-xs text-blue-600 mt-2">
            * 这将是答案页面的URL路径
          </p>
        </div>
      )}

      {/* 实时验证状态 */}
      <div className={`border rounded-lg p-3 ${
        validation.isValid 
          ? 'bg-green-50 border-green-200' 
          : 'bg-yellow-50 border-yellow-200'
      }`}>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            validation.isValid ? 'bg-green-500' : 'bg-yellow-500'
          }`} />
          <span className={`text-sm font-medium ${
            validation.isValid ? 'text-green-800' : 'text-yellow-800'
          }`}>
            {validation.isValid ? '表单验证通过' : '表单需要完善'}
          </span>
        </div>
      </div>
    </div>
  );
}

// 实时表单验证Hook
export function usePinpointFormValidation(initialData?: Partial<PinpointFormData>) {
  const [formData, setFormData] = useState<Partial<PinpointFormData>>(initialData || {});
  const [isValid, setIsValid] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const [urlPreview, setUrlPreview] = useState<string>("");

  const updateField = (field: keyof PinpointFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateClueWord = (index: number, value: string) => {
    setFormData(prev => {
      const clueWords = [...(prev.clueWords || ['', '', '', '', ''])];
      clueWords[index] = value;
      return {
        ...prev,
        clueWords: clueWords as [string, string, string, string, string],
      };
    });
  };

  const handleValidationChange = (valid: boolean, errorMessages: string[]) => {
    setIsValid(valid);
    setErrors(errorMessages);
  };

  const handleUrlPreviewChange = (slug: string) => {
    setUrlPreview(slug);
  };

  const reset = () => {
    setFormData({});
    setIsValid(false);
    setErrors([]);
    setUrlPreview("");
  };

  return {
    formData,
    isValid,
    errors,
    urlPreview,
    updateField,
    updateClueWord,
    handleValidationChange,
    handleUrlPreviewChange,
    reset,
    setFormData,
  };
}

// 表单提交处理器
export async function submitPinpointForm(
  formData: PinpointFormData,
  apiUrl: string,
  method: 'POST' | 'PUT' = 'POST'
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    // 最终验证
    const validation = validatePinpointData(formData);
    if (!validation.isValid) {
      return {
        success: false,
        error: `表单验证失败: ${validation.errors.map(e => e.message).join(', ')}`,
      };
    }

    const response = await fetch(apiUrl, {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData),
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.message || '提交失败',
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error('表单提交错误:', error);
    return {
      success: false,
      error: '网络错误，请重试',
    };
  }
}

// 错误提示组件
export function FormErrorAlert({ errors }: { errors: string[] }) {
  if (errors.length === 0) return null;

  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <h3 className="text-sm font-medium text-red-800 mb-2">⚠️ 请修正以下错误：</h3>
      <ul className="text-sm text-red-700 space-y-1">
        {errors.map((error, index) => (
          <li key={index}>• {error}</li>
        ))}
      </ul>
    </div>
  );
}

// 成功提示组件
export function FormSuccessAlert({ message }: { message: string }) {
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs">✓</span>
        </div>
        <span className="text-sm font-medium text-green-800">{message}</span>
      </div>
    </div>
  );
}
