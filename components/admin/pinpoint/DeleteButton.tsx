"use client";

interface DeleteButtonProps {
  answerId: number;
  gameNumber: number;
}

export default function DeleteButton({ answerId, gameNumber }: DeleteButtonProps) {
  const handleDelete = async () => {
    if (confirm(`确定要删除游戏 #${gameNumber} 的答案吗？此操作不可逆！`)) {
      try {
        const response = await fetch(`/api/admin/pinpoint/${answerId}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          alert('答案已删除');
          window.location.href = '/admin/pinpoint';
        } else {
          const error = await response.json();
          alert(`删除失败: ${error.message}`);
        }
      } catch (error) {
        alert('删除失败，请重试');
      }
    }
  };

  return (
    <button
      onClick={handleDelete}
      className="px-4 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
    >
      删除此答案
    </button>
  );
}
