"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Search, 
  Calendar, 
  Hash, 
  ArrowLeft, 
  ChevronLeft, 
  ChevronRight,
  Filter,
  SortAsc,
  SortDesc
} from "lucide-react";

interface HistoryAnswersPageProps {
  answers: PinpointAnswerFormatted[];
  currentPage: number;
  totalPages: number;
  totalAnswers: number;
  searchQuery: string;
  error?: string;
}

export default function HistoryAnswersPage({
  answers,
  currentPage,
  totalPages,
  totalAnswers,
  searchQuery,
  error,
}: HistoryAnswersPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [localSearch, setLocalSearch] = useState(searchQuery);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams);
    if (localSearch) {
      params.set("search", localSearch);
    } else {
      params.delete("search");
    }
    params.delete("page"); // 重置到第一页
    router.push(`/history?${params.toString()}`);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`/history?${params.toString()}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const generatePageNumbers = () => {
    const pages = [];
    const maxVisible = 5;
    
    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const start = Math.max(1, currentPage - 2);
      const end = Math.min(totalPages, start + maxVisible - 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">
              历史答案
            </h1>
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
            <Link href="/">
              <Button>返回首页</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>

        {/* 页面标题和搜索 */}
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              历史答案
            </h1>
            <p className="text-xl text-gray-600 mb-6">
              浏览所有LinkedIn Pinpoint历史答案
            </p>

            {/* 搜索栏 */}
            <form onSubmit={handleSearch} className="max-w-md mx-auto mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="搜索答案、线索词或游戏编号..."
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  className="pl-10 pr-4 py-2"
                />
                <Button
                  type="submit"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2"
                >
                  搜索
                </Button>
              </div>
            </form>

            {/* 统计信息 */}
            <div className="flex justify-center items-center gap-4 text-sm text-gray-600">
              <span>共找到 {totalAnswers} 个答案</span>
              {searchQuery && (
                <Badge variant="outline">
                  搜索: "{searchQuery}"
                </Badge>
              )}
            </div>
          </div>

          {/* 答案列表 */}
          {answers.length === 0 ? (
            <Card className="shadow-lg">
              <CardContent className="p-8 text-center">
                <p className="text-gray-500 text-lg">
                  {searchQuery ? "未找到匹配的答案" : "暂无历史答案"}
                </p>
                {searchQuery && (
                  <Link href="/history">
                    <Button variant="outline" className="mt-4">
                      查看所有答案
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {answers.map((answer) => (
                  <Link
                    key={answer.gameNumber}
                    href={`/${answer.urlSlug}`}
                    className="block hover:transform hover:scale-105 transition-all duration-200"
                  >
                    <Card className="shadow-lg hover:shadow-xl h-full">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2 text-blue-600">
                            <Hash className="w-4 h-4" />
                            <span className="font-bold">#{answer.gameNumber}</span>
                          </div>
                          <div className="flex items-center gap-1 text-gray-500 text-sm">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(answer.date)}</span>
                          </div>
                        </div>
                        <CardTitle className="text-lg line-clamp-2">
                          {answer.answer}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div>
                            <p className="text-sm text-gray-600 mb-2">线索词:</p>
                            <div className="flex flex-wrap gap-1">
                              {answer.clues.map((clue, index) => (
                                <span
                                  key={index}
                                  className="inline-block bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded"
                                >
                                  {clue}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="w-4 h-4" />
                    上一页
                  </Button>

                  {generatePageNumbers().map((page) => (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  ))}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    下一页
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
