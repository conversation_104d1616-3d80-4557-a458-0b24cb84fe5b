"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Calendar,
  Hash,
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  ArrowRight,
  Filter,
  SortAsc,
  SortDesc
} from "lucide-react";

interface HistoryAnswersPageProps {
  answers: PinpointAnswerFormatted[];
  currentPage: number;
  totalPages: number;
  totalAnswers: number;
  searchQuery: string;
  error?: string;
}

export default function HistoryAnswersPage({
  answers,
  currentPage,
  totalPages,
  totalAnswers,
  searchQuery,
  error,
}: HistoryAnswersPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [localSearch, setLocalSearch] = useState(searchQuery);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams);
    if (localSearch) {
      params.set("search", localSearch);
    } else {
      params.delete("search");
    }
    params.delete("page"); // 重置到第一页
    router.push(`/history?${params.toString()}`);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`/history?${params.toString()}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const generatePageNumbers = () => {
    const pages = [];
    const maxVisible = 5;
    
    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const start = Math.max(1, currentPage - 2);
      const end = Math.min(totalPages, start + maxVisible - 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  if (error) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
        <div className="container mx-auto px-6 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <h1 className="apple-title mb-8">历史答案</h1>
            <div className="apple-card p-8 mb-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: '#FF3B30' }}>
                <span className="text-2xl text-white">⚠️</span>
              </div>
              <p className="apple-body mb-6" style={{ color: '#FF3B30' }}>
                {error}
              </p>
              <Link href="/" className="apple-button apple-button-primary">
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="container mx-auto px-6 py-8">
        {/* 导航栏 */}
        <div className="mb-12">
          <Link href="/" className="apple-button apple-button-secondary">
            <ArrowLeft className="w-4 h-4" />
            返回首页
          </Link>
        </div>

        {/* 页面标题和搜索 */}
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="apple-title-large mb-6">历史答案</h1>
            <p className="apple-body-large mb-12 max-w-2xl mx-auto">
              浏览所有LinkedIn Pinpoint历史答案，快速找到您需要的游戏解答
            </p>

            {/* 搜索栏 */}
            <form onSubmit={handleSearch} className="max-w-lg mx-auto mb-8">
              <div className="relative flex items-center gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5"
                          style={{ color: 'var(--color-text-tertiary)' }} />
                  <input
                    type="text"
                    placeholder="搜索答案、线索词或游戏编号..."
                    value={localSearch}
                    onChange={(e) => setLocalSearch(e.target.value)}
                    className="apple-input pl-12 pr-4 py-4 text-lg w-full"
                  />
                </div>
                <button
                  type="submit"
                  className="apple-button apple-button-primary px-6 py-4 text-lg flex-shrink-0"
                >
                  搜索
                </button>
              </div>
            </form>

            {/* 统计信息 */}
            <div className="flex justify-center items-center gap-6">
              <span className="apple-body">共找到 {totalAnswers} 个答案</span>
              {searchQuery && (
                <div className="px-4 py-2 rounded-full text-sm font-medium"
                     style={{
                       backgroundColor: 'var(--color-primary)',
                       color: 'white'
                     }}>
                  搜索: "{searchQuery}"
                </div>
              )}
            </div>
          </div>

          {/* 答案列表 */}
          {answers.length === 0 ? (
            <div className="apple-card p-16 text-center">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
                <Search className="w-10 h-10" style={{ color: 'var(--color-text-secondary)' }} />
              </div>
              <h3 className="apple-subtitle mb-4">
                {searchQuery ? "未找到匹配的答案" : "暂无历史答案"}
              </h3>
              <p className="apple-body mb-8">
                {searchQuery ? "尝试使用其他关键词搜索" : "历史答案将在这里显示"}
              </p>
              {searchQuery && (
                <Link href="/history" className="apple-button apple-button-primary">
                  查看所有答案
                </Link>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
                {answers.map((answer, index) => (
                  <Link
                    key={answer.gameNumber}
                    href={`/${answer.urlSlug}`}
                    className="block group"
                  >
                    <div className="apple-card p-6 h-full transition-all duration-300 hover:scale-[1.02] hover:shadow-lg">
                      {/* Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full flex items-center justify-center"
                               style={{ backgroundColor: 'var(--color-primary)' }}>
                            <Hash className="w-5 h-5 text-white" />
                          </div>
                          <span className="font-semibold apple-body" style={{ color: 'var(--color-primary)' }}>
                            #{answer.gameNumber}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 apple-caption">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(answer.date)}</span>
                        </div>
                      </div>

                      {/* Mystery Answer Hint */}
                      <div className="mb-4 flex items-center gap-2">
                        <span className="text-sm font-medium" style={{ color: 'var(--color-text-secondary)' }}>
                          🤔 猜猜答案是什么？
                        </span>
                        <div className="flex gap-1">
                          <span className="inline-block w-8 h-3 rounded-sm animate-pulse"
                                style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
                          <span className="inline-block w-12 h-3 rounded-sm animate-pulse"
                                style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
                          <span className="inline-block w-6 h-3 rounded-sm animate-pulse"
                                style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
                        </div>
                      </div>

                      {/* Clues */}
                      <div className="space-y-3">
                        <p className="apple-caption">线索词:</p>
                        <div className="flex flex-wrap gap-2">
                          {answer.clues.map((clue, clueIndex) => (
                            <span
                              key={clueIndex}
                              className="inline-block px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 group-hover:scale-105"
                              style={{
                                backgroundColor: 'var(--color-primary)',
                                color: 'white'
                              }}
                            >
                              {clue}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Arrow indicator */}
                      <div className="mt-4 flex justify-end">
                        <div className="w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 group-hover:bg-blue-50 group-hover:scale-110">
                          <ArrowRight className="w-4 h-4" style={{ color: 'var(--color-text-tertiary)' }} />
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center gap-3">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`apple-button ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'apple-button-secondary'}`}
                  >
                    <ChevronLeft className="w-4 h-4" />
                    上一页
                  </button>

                  <div className="flex items-center gap-2">
                    {generatePageNumbers().map((page) => (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                          page === currentPage
                            ? 'text-white'
                            : 'hover:scale-110'
                        }`}
                        style={{
                          backgroundColor: page === currentPage ? 'var(--color-primary)' : 'transparent',
                          color: page === currentPage ? 'white' : 'var(--color-text-secondary)'
                        }}
                      >
                        {page}
                      </button>
                    ))}
                  </div>

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`apple-button ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'apple-button-secondary'}`}
                  >
                    下一页
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
