"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { getNextReleaseCountdown } from "@/lib/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, Hash, Eye, EyeOff } from "lucide-react";

interface TodayAnswerPreviewProps {
  todayAnswer: PinpointAnswerFormatted | null;
}

export default function TodayAnswerPreview({ todayAnswer }: TodayAnswerPreviewProps) {
  const [countdown, setCountdown] = useState("00:00:00");
  const [showAnswer, setShowAnswer] = useState(false);

  useEffect(() => {
    const updateCountdown = () => {
      const countdownData = getNextReleaseCountdown();

      if (countdownData.totalMs <= 0) {
        setCountdown('00:00:00');
        return;
      }

      setCountdown(
        `${countdownData.hours.toString().padStart(2, '0')}:${countdownData.minutes
          .toString()
          .padStart(2, '0')}:${countdownData.seconds.toString().padStart(2, '0')}`
      );
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      month: "long",
      day: "numeric",
      weekday: "long",
    });
  };

  if (!todayAnswer) {
    return (
      <div className="apple-card p-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Clock className="w-8 h-8" style={{ color: 'var(--color-text-secondary)' }} />
          </div>
          <h2 className="apple-subtitle mb-4">今日暂无答案</h2>
          <p className="apple-body mb-8">今日的Pinpoint答案尚未发布</p>
          <div className="inline-flex items-center gap-3 px-6 py-4 rounded-2xl"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Clock className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
            <div>
              <div className="text-3xl font-mono font-bold" style={{ color: 'var(--color-primary)' }}>
                {countdown}
              </div>
              <p className="apple-caption mt-1">距离下一个谜题发布</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="apple-card p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-primary)' }}>
            <Hash className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="apple-subtitle">今日 Pinpoint</h2>
            <p className="apple-caption">#{todayAnswer.gameNumber}</p>
          </div>
        </div>
        <div className="flex items-center justify-center gap-2 apple-caption">
          <Calendar className="w-4 h-4" />
          <span>{formatDate(todayAnswer.date)}</span>
        </div>
      </div>

      {/* 线索词 */}
      <div className="mb-8">
        <h3 className="apple-subtitle text-center mb-6">线索词</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {todayAnswer.clues.map((clue, index) => (
            <div
              key={index}
              className="relative overflow-hidden rounded-xl p-4 text-center transition-all duration-300 hover:scale-105"
              style={{
                background: 'linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%)',
                color: 'white'
              }}
            >
              <div className="apple-caption opacity-80 mb-1">线索 {index + 1}</div>
              <div className="font-semibold">{clue}</div>
              <div className="absolute inset-0 bg-white opacity-0 hover:opacity-10 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>
      </div>

      {/* 答案预览 */}
      <div className="text-center mb-8">
        {showAnswer ? (
          <div className="space-y-6">
            <h2 className="apple-title" style={{ color: 'var(--color-text-primary)' }}>
              {todayAnswer.answer}
            </h2>
            <div className="inline-flex items-center px-6 py-3 rounded-full text-base font-medium"
                 style={{
                   backgroundColor: 'var(--color-primary)',
                   color: 'white'
                 }}>
              ✨ 今日答案
            </div>
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setShowAnswer(false)}
                className="apple-button apple-button-secondary"
              >
                <EyeOff className="w-4 h-4" />
                隐藏答案
              </button>
              <Link href={`/${todayAnswer.urlSlug}`} className="apple-button apple-button-primary">
                查看详情
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 神秘答案显示 */}
            <div className="relative">
              <div className="apple-title mb-4 flex items-center justify-center gap-2">
                <span className="inline-block w-16 h-8 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
                <span className="inline-block w-20 h-8 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
                <span className="inline-block w-12 h-8 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
              </div>
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="text-4xl opacity-20">🤔</div>
              </div>
            </div>

            <button
              onClick={() => setShowAnswer(true)}
              className="inline-flex items-center px-6 py-3 rounded-full text-base font-medium border-2 border-dashed transition-all duration-300 hover:scale-105"
              style={{
                borderColor: 'var(--color-primary)',
                color: 'var(--color-primary)',
                backgroundColor: 'rgba(0, 122, 255, 0.05)'
              }}
            >
              🔍 点击揭晓答案
            </button>
          </div>
        )}
      </div>

      {/* 下一个谜题倒计时 */}
      <div className="pt-8 border-t text-center" style={{ borderColor: 'var(--color-border-light)' }}>
        <div className="inline-flex items-center gap-3 px-6 py-4 rounded-2xl"
             style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
          <Clock className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
          <div>
            <p className="apple-caption mb-1">下一个谜题发布倒计时</p>
            <div className="text-2xl font-mono font-bold" style={{ color: 'var(--color-primary)' }}>
              {countdown}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
