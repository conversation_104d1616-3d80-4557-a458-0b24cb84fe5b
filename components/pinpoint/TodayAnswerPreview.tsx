"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { calculateCountdown, formatCountdown, getNextPuzzleTime } from "@/lib/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, Hash, Eye, EyeOff } from "lucide-react";

interface TodayAnswerPreviewProps {
  todayAnswer: PinpointAnswerFormatted | null;
}

export default function TodayAnswerPreview({ todayAnswer }: TodayAnswerPreviewProps) {
  const [countdown, setCountdown] = useState("00:00:00");
  const [showAnswer, setShowAnswer] = useState(false);

  useEffect(() => {
    const updateCountdown = () => {
      const nextPuzzleTime = getNextPuzzleTime();
      const countdownData = calculateCountdown(nextPuzzleTime);
      setCountdown(formatCountdown(countdownData));
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      month: "long",
      day: "numeric",
      weekday: "long",
    });
  };

  if (!todayAnswer) {
    return (
      <Card className="shadow-lg">
        <CardHeader className="text-center bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-t-lg">
          <CardTitle className="text-2xl font-bold">今日暂无答案</CardTitle>
        </CardHeader>
        <CardContent className="p-6 text-center">
          <p className="text-gray-600 mb-4">今日的Pinpoint答案尚未发布</p>
          <div className="text-2xl font-mono font-bold text-blue-600 mb-2">
            {countdown}
          </div>
          <p className="text-sm text-gray-500">距离下一个谜题发布</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-lg">
      <CardHeader className="text-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Hash className="w-5 h-5" />
          <CardTitle className="text-2xl font-bold">
            今日 Pinpoint #{todayAnswer.gameNumber}
          </CardTitle>
        </div>
        <div className="flex items-center justify-center gap-2 text-blue-100">
          <Calendar className="w-4 h-4" />
          <span>{formatDate(todayAnswer.date)}</span>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {/* 线索词 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-3 text-center">
            线索词
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
            {todayAnswer.clues.map((clue, index) => (
              <div
                key={index}
                className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white p-3 rounded-lg text-center font-medium shadow-md"
              >
                <div className="text-xs opacity-80 mb-1">线索 {index + 1}</div>
                <div className="font-bold">{clue}</div>
              </div>
            ))}
          </div>
        </div>

        {/* 答案预览 */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAnswer(!showAnswer)}
              className="flex items-center gap-2"
            >
              {showAnswer ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showAnswer ? "隐藏答案" : "查看答案"}
            </Button>
          </div>
          
          {showAnswer ? (
            <div className="space-y-3">
              <h2 className="text-2xl font-bold text-gray-800">
                {todayAnswer.answer}
              </h2>
              <Badge variant="secondary" className="text-base px-3 py-1">
                今日答案
              </Badge>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="text-2xl font-bold text-gray-400">
                ??? ??? ???
              </div>
              <Badge variant="outline" className="text-base px-3 py-1">
                点击查看答案
              </Badge>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-center gap-4">
          <Link href={`/${todayAnswer.urlSlug}`}>
            <Button className="flex items-center gap-2">
              查看详情
            </Button>
          </Link>
        </div>

        {/* 下一个谜题倒计时 */}
        <div className="mt-6 pt-6 border-t border-gray-200 text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Clock className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">下一个谜题发布倒计时</span>
          </div>
          <div className="text-xl font-mono font-bold text-blue-600">
            {countdown}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
