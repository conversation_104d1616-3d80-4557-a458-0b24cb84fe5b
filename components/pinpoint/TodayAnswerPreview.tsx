"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { getNextReleaseCountdown } from "@/lib/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, Hash, Eye, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";

interface TodayAnswerPreviewProps {
  todayAnswer: PinpointAnswerFormatted | null;
}

export default function TodayAnswerPreview({ todayAnswer }: TodayAnswerPreviewProps) {
  const [countdown, setCountdown] = useState("00:00:00");
  const [showAnswer, setShowAnswer] = useState(false);
  const t = useTranslations("pinpoint");
  const params = useParams();
  const locale = params.locale as string;

  // 计算下一个谜题编号
  const nextPuzzleNumber = todayAnswer ? todayAnswer.gameNumber + 1 : 1;

  useEffect(() => {
    const updateCountdown = () => {
      const countdownData = getNextReleaseCountdown();

      if (countdownData.totalMs <= 0) {
        setCountdown('00:00:00');
        return;
      }

      setCountdown(
        `${countdownData.hours.toString().padStart(2, '0')}:${countdownData.minutes
          .toString()
          .padStart(2, '0')}:${countdownData.seconds.toString().padStart(2, '0')}`
      );
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);

    // 根据当前语言环境格式化日期
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";

    if (locale === "zh") {
      const month = date.toLocaleDateString(localeCode, { month: "long" });
      const day = date.getDate();
      const weekday = date.toLocaleDateString(localeCode, { weekday: "long" });
      return t("date_format", { month, day, weekday });
    } else {
      const month = date.toLocaleDateString(localeCode, { month: "long" });
      const day = date.getDate();
      const weekday = date.toLocaleDateString(localeCode, { weekday: "long" });
      return t("date_format", { month, day, weekday });
    }
  };

  if (!todayAnswer) {
    return (
      <div className="apple-card p-8">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Clock className="w-8 h-8" style={{ color: 'var(--color-text-secondary)' }} />
          </div>
          <h2 className="apple-subtitle mb-4">{t("no_answer_today")}</h2>
          <p className="apple-body mb-8">{t("no_answer_description")}</p>
          <div className="inline-flex items-center gap-3 px-6 py-4 rounded-2xl"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Clock className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
            <div>
              <div className="text-3xl font-mono font-bold" style={{ color: 'var(--color-primary)' }}>
                {countdown}
              </div>
              <p className="apple-caption mt-1">
                {locale === "zh" ? (
                  <>
                    下一个谜题{" "}
                    <span className="font-bold px-2 py-1 rounded-md text-white"
                          style={{ backgroundColor: 'var(--color-primary)' }}>
                      LinkedIn Pinpoint #{nextPuzzleNumber}
                    </span>
                    {" "}倒计时
                  </>
                ) : (
                  <>
                    Next Puzzle{" "}
                    <span className="font-bold px-2 py-1 rounded-md text-white"
                          style={{ backgroundColor: 'var(--color-primary)' }}>
                      LinkedIn Pinpoint #{nextPuzzleNumber}
                    </span>
                    {" "}Countdown
                  </>
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="apple-card p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-primary)' }}>
            <Hash className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="apple-subtitle">LinkedIn Pinpoint #{todayAnswer.gameNumber} {todayAnswer.clues.join(' ')}</h2>
          </div>
        </div>
        <div className="flex items-center justify-center gap-2 apple-caption">
          <Calendar className="w-4 h-4" />
          <span>{formatDate(todayAnswer.date)}</span>
        </div>
      </div>

      {/* 线索词 */}
      <div className="mb-8">
        <h3 className="apple-subtitle text-center mb-6">{t("clues")}</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {todayAnswer.clues.map((clue, index) => (
            <div
              key={index}
              className="relative overflow-hidden rounded-xl p-4 text-center transition-all duration-300 hover:scale-105"
              style={{
                background: 'linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%)',
                color: 'white'
              }}
            >
              <div className="apple-caption opacity-80 mb-1">{t("clue_number", { number: index + 1 })}</div>
              <div className="font-semibold">{clue}</div>
              <div className="absolute inset-0 bg-white opacity-0 hover:opacity-10 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>
      </div>

      {/* 答案预览 */}
      <div className="text-center mb-8">
        {showAnswer ? (
          <div className="space-y-6">
            <h3 className="apple-title" style={{ color: 'var(--color-text-primary)' }}>
              {todayAnswer.answer}
            </h3>
            <div className="inline-flex items-center px-6 py-3 rounded-full text-base font-medium"
                 style={{
                   backgroundColor: 'var(--color-primary)',
                   color: 'white'
                 }}>
              ✨ {t("today_answer")}
            </div>
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setShowAnswer(false)}
                className="apple-button apple-button-secondary"
              >
                <EyeOff className="w-4 h-4" />
                {t("hide_answer")}
              </button>
              <Link href={`/${todayAnswer.urlSlug}`} className="apple-button apple-button-primary">
                {t("view_details")}
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 神秘答案显示 */}
            <div className="relative">
              <div className="apple-title mb-4 flex items-center justify-center gap-2">
                <span className="inline-block w-16 h-8 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
                <span className="inline-block w-20 h-8 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
                <span className="inline-block w-12 h-8 rounded-lg animate-pulse"
                      style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
              </div>
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="text-4xl opacity-20">🤔</div>
              </div>
            </div>

            <button
              onClick={() => setShowAnswer(true)}
              className="inline-flex items-center px-6 py-3 rounded-full text-base font-medium border-2 border-dashed transition-all duration-300 hover:scale-105"
              style={{
                borderColor: 'var(--color-primary)',
                color: 'var(--color-primary)',
                backgroundColor: 'rgba(0, 122, 255, 0.05)'
              }}
            >
              {t("reveal_answer")}
            </button>
          </div>
        )}
      </div>

      {/* 下一个谜题倒计时 */}
      <div className="pt-8 border-t text-center" style={{ borderColor: 'var(--color-border-light)' }}>
        <div className="inline-flex items-center gap-3 px-6 py-4 rounded-2xl"
             style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
          <Clock className="w-5 h-5" style={{ color: 'var(--color-primary)' }} />
          <div>
            <p className="apple-caption mb-1">
              {locale === "zh" ? (
                <>
                  下一个谜题{" "}
                  <span className="font-bold px-2 py-1 rounded-md text-white"
                        style={{ backgroundColor: 'var(--color-primary)' }}>
                    LinkedIn Pinpoint #{nextPuzzleNumber}
                  </span>
                  {" "}倒计时
                </>
              ) : (
                <>
                  Next Puzzle{" "}
                  <span className="font-bold px-2 py-1 rounded-md text-white"
                        style={{ backgroundColor: 'var(--color-primary)' }}>
                    LinkedIn Pinpoint #{nextPuzzleNumber}
                  </span>
                  {" "}Countdown
                </>
              )}
            </p>
            <div className="text-2xl font-mono font-bold" style={{ color: 'var(--color-primary)' }}>
              {countdown}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
