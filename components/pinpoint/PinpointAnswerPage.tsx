"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { calculateCountdown, formatCountdown, getNextPuzzleTime } from "@/lib/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, Hash, ArrowLeft, Share2, Copy } from "lucide-react";

interface PinpointAnswerPageProps {
  answer: PinpointAnswerFormatted;
}

export default function PinpointAnswerPage({ answer }: PinpointAnswerPageProps) {
  const [countdown, setCountdown] = useState("00:00:00");
  const [copied, setCopied] = useState(false);

  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations("answer_detail");
  const tPinpoint = useTranslations("pinpoint");
  const tHomepage = useTranslations("homepage");

  useEffect(() => {
    const updateCountdown = () => {
      const nextPuzzleTime = getNextPuzzleTime();
      const countdownData = calculateCountdown(nextPuzzleTime);
      setCountdown(formatCountdown(countdownData));
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleShare = async () => {
    const url = window.location.href;
    const text = locale === "zh"
      ? `Pinpoint #${answer.gameNumber} 答案: ${answer.answer}`
      : `Pinpoint #${answer.gameNumber} Answer: ${answer.answer}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: text,
          url: url,
        });
      } catch (error) {
        // 如果分享失败，回退到复制链接
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("复制失败:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";

    return date.toLocaleDateString(localeCode, {
      year: "numeric",
      month: "long",
      day: "numeric",
      weekday: "long",
    });
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="container mx-auto px-6 py-8">
        {/* 导航栏 */}
        <div className="mb-12 flex gap-4">
          <Link href={locale === "en" ? "/" : `/${locale}`} className="apple-button apple-button-secondary">
            <ArrowLeft className="w-4 h-4" />
            {t("back_to_home")}
          </Link>
          <Link href={locale === "en" ? "/history" : `/${locale}/history`} className="apple-button apple-button-secondary">
            <ArrowLeft className="w-4 h-4" />
            {t("back_to_history")}
          </Link>
        </div>

        {/* 主要内容 */}
        <div className="max-w-5xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-4 mb-6">
              <div className="w-16 h-16 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: 'var(--color-primary)' }}>
                <Hash className="w-8 h-8 text-white" />
              </div>
              <div className="text-left">
                <h1 className="apple-title-large">LinkedIn Pinpoint #{answer.gameNumber} Answer</h1>
                <div className="flex items-center gap-2 apple-caption mt-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(answer.date)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 答案展示 */}
          <div className="apple-card p-12 mb-12 text-center">
            <h2 className="apple-title-hero mb-8" style={{ color: 'var(--color-text-primary)' }}>
              {answer.answer}
            </h2>
            <div className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium"
                 style={{
                   backgroundColor: 'var(--color-primary)',
                   color: 'white'
                 }}>
              {t("answer_revealed")}
            </div>
          </div>

          {/* 线索词 */}
          <div className="apple-card p-12 mb-12">
            <h3 className="apple-title text-center mb-12">{t("clues")}</h3>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              {answer.clues.map((clue, index) => (
                <div
                  key={index}
                  className="relative overflow-hidden rounded-2xl p-6 text-center transition-all duration-500 hover:scale-105 group"
                  style={{
                    background: 'linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%)',
                    color: 'white'
                  }}
                >
                  <div className="apple-caption opacity-80 mb-2">{t("clue_number", { number: index + 1 })}</div>
                  <div className="text-xl font-semibold">{clue}</div>
                  <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                  <div className="absolute -top-4 -right-4 w-8 h-8 rounded-full opacity-20"
                       style={{ backgroundColor: 'white' }}></div>
                </div>
              ))}
            </div>
          </div>

          {/* 分享按钮 */}
          <div className="flex justify-center gap-6 mb-12">
            <button onClick={handleShare} className="apple-button apple-button-primary">
              <Share2 className="w-5 h-5" />
              {t("share")}
            </button>
            <button
              onClick={handleCopyLink}
              className="apple-button apple-button-secondary"
            >
              <Copy className="w-5 h-5" />
              {copied ? t("link_copied") : t("copy_link")}
            </button>
          </div>

          {/* 下一个谜题倒计时 */}
          <div className="apple-card p-12 mb-12 text-center">
            <div className="inline-flex items-center gap-4 mb-8">
              <div className="w-12 h-12 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: 'var(--color-primary)' }}>
                <Clock className="w-6 h-6 text-white" />
              </div>
              <h3 className="apple-subtitle">{t("countdown_label")}</h3>
            </div>
            <div className="text-6xl font-mono font-bold mb-6"
                 style={{ color: 'var(--color-primary)' }}>
              {countdown}
            </div>
            <p className="apple-body">
              {t("puzzle_release_info")}
            </p>
          </div>

          {/* 游戏说明 */}
          <div className="apple-card p-12">
            <h3 className="apple-title text-center mb-12">{t("about_pinpoint")}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl flex-shrink-0"
                       style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                    🎯
                  </div>
                  <div>
                    <h4 className="apple-subtitle mb-4">{t("game_rules_section")}</h4>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">{tHomepage("rule_1")}</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">{tHomepage("rule_2")}</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">{tHomepage("rule_3")}</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">{tHomepage("rule_4")}</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl flex-shrink-0"
                       style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                    📱
                  </div>
                  <div>
                    <h4 className="apple-subtitle mb-4">{t("quick_navigation")}</h4>
                    <div className="space-y-4">
                      <Link href={locale === "en" ? "/history" : `/${locale}/history`} className="apple-button apple-button-secondary w-full justify-center">
                        {t("view_history")}
                      </Link>
                      <Link href={locale === "en" ? "/" : `/${locale}`} className="apple-button apple-button-primary w-full justify-center">
                        {t("back_home")}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
