"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { calculateCountdown, formatCountdown, getNextPuzzleTime } from "@/lib/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, Hash, ArrowLeft, Share2, Copy } from "lucide-react";

interface PinpointAnswerPageProps {
  answer: PinpointAnswerFormatted;
}

export default function PinpointAnswerPage({ answer }: PinpointAnswerPageProps) {
  const [countdown, setCountdown] = useState("00:00:00");
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const updateCountdown = () => {
      const nextPuzzleTime = getNextPuzzleTime();
      const countdownData = calculateCountdown(nextPuzzleTime);
      setCountdown(formatCountdown(countdownData));
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleShare = async () => {
    const url = window.location.href;
    const text = `Pinpoint #${answer.gameNumber} 答案: ${answer.answer}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: text,
          url: url,
        });
      } catch (error) {
        // 如果分享失败，回退到复制链接
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("复制失败:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      weekday: "long",
    });
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-background)' }}>
      <div className="container mx-auto px-6 py-8">
        {/* 导航栏 */}
        <div className="mb-12">
          <Link href="/" className="apple-button apple-button-secondary">
            <ArrowLeft className="w-4 h-4" />
            返回首页
          </Link>
        </div>

        {/* 主要内容 */}
        <div className="max-w-5xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-4 mb-6">
              <div className="w-16 h-16 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: 'var(--color-primary)' }}>
                <Hash className="w-8 h-8 text-white" />
              </div>
              <div className="text-left">
                <h1 className="apple-title-large">Pinpoint #{answer.gameNumber}</h1>
                <div className="flex items-center gap-2 apple-caption mt-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(answer.date)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 答案展示 */}
          <div className="apple-card p-12 mb-12 text-center">
            <h2 className="apple-title-hero mb-8" style={{ color: 'var(--color-text-primary)' }}>
              {answer.answer}
            </h2>
            <div className="inline-flex items-center px-6 py-3 rounded-full text-lg font-medium"
                 style={{
                   backgroundColor: 'var(--color-primary)',
                   color: 'white'
                 }}>
              今日答案
            </div>
          </div>

          {/* 线索词 */}
          <div className="apple-card p-12 mb-12">
            <h3 className="apple-title text-center mb-12">线索词</h3>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              {answer.clues.map((clue, index) => (
                <div
                  key={index}
                  className="relative overflow-hidden rounded-2xl p-6 text-center transition-all duration-500 hover:scale-105 group"
                  style={{
                    background: 'linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%)',
                    color: 'white'
                  }}
                >
                  <div className="apple-caption opacity-80 mb-2">线索 {index + 1}</div>
                  <div className="text-xl font-semibold">{clue}</div>
                  <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                  <div className="absolute -top-4 -right-4 w-8 h-8 rounded-full opacity-20"
                       style={{ backgroundColor: 'white' }}></div>
                </div>
              ))}
            </div>
          </div>

          {/* 分享按钮 */}
          <div className="flex justify-center gap-6 mb-12">
            <button onClick={handleShare} className="apple-button apple-button-primary">
              <Share2 className="w-5 h-5" />
              分享答案
            </button>
            <button
              onClick={handleCopyLink}
              className="apple-button apple-button-secondary"
            >
              <Copy className="w-5 h-5" />
              {copied ? "已复制!" : "复制链接"}
            </button>
          </div>

          {/* 下一个谜题倒计时 */}
          <div className="apple-card p-12 mb-12 text-center">
            <div className="inline-flex items-center gap-4 mb-8">
              <div className="w-12 h-12 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: 'var(--color-primary)' }}>
                <Clock className="w-6 h-6 text-white" />
              </div>
              <h3 className="apple-subtitle">下一个谜题发布倒计时</h3>
            </div>
            <div className="text-6xl font-mono font-bold mb-6"
                 style={{ color: 'var(--color-primary)' }}>
              {countdown}
            </div>
            <p className="apple-body">
              新的Pinpoint谜题将在太平洋时间午夜发布
            </p>
          </div>

          {/* 游戏说明 */}
          <div className="apple-card p-12">
            <h3 className="apple-title text-center mb-12">关于 LinkedIn Pinpoint</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl flex-shrink-0"
                       style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                    🎯
                  </div>
                  <div>
                    <h4 className="apple-subtitle mb-4">游戏规则</h4>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">每天发布一个新的谜题</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">根据5个线索词找出共同点</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">答案通常是一个类别、主题或概念</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="w-2 h-2 rounded-full mt-2 flex-shrink-0"
                              style={{ backgroundColor: 'var(--color-primary)' }}></span>
                        <span className="apple-body">每个谜题都有唯一的编号</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-2xl flex-shrink-0"
                       style={{ backgroundColor: 'var(--color-primary)', color: 'white' }}>
                    📱
                  </div>
                  <div>
                    <h4 className="apple-subtitle mb-4">快速导航</h4>
                    <div className="space-y-4">
                      <Link href="/history" className="apple-button apple-button-secondary w-full justify-center">
                        查看历史答案
                      </Link>
                      <Link href="/" className="apple-button apple-button-primary w-full justify-center">
                        返回首页
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
