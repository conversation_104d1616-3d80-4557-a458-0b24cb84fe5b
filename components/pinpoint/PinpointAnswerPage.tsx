"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { calculateCountdown, formatCountdown, getNextPuzzleTime } from "@/lib/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Calendar, Hash, ArrowLeft, Share2, Copy } from "lucide-react";

interface PinpointAnswerPageProps {
  answer: PinpointAnswerFormatted;
}

export default function PinpointAnswerPage({ answer }: PinpointAnswerPageProps) {
  const [countdown, setCountdown] = useState("00:00:00");
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    const updateCountdown = () => {
      const nextPuzzleTime = getNextPuzzleTime();
      const countdownData = calculateCountdown(nextPuzzleTime);
      setCountdown(formatCountdown(countdownData));
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleShare = async () => {
    const url = window.location.href;
    const text = `Pinpoint #${answer.gameNumber} 答案: ${answer.answer}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: text,
          url: url,
        });
      } catch (error) {
        // 如果分享失败，回退到复制链接
        handleCopyLink();
      }
    } else {
      handleCopyLink();
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("复制失败:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "long",
      day: "numeric",
      weekday: "long",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>

        {/* 主要内容 */}
        <div className="max-w-4xl mx-auto">
          {/* 答案卡片 */}
          <Card className="mb-8 shadow-lg">
            <CardHeader className="text-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Hash className="w-6 h-6" />
                <CardTitle className="text-3xl font-bold">
                  Pinpoint #{answer.gameNumber}
                </CardTitle>
              </div>
              <div className="flex items-center justify-center gap-2 text-blue-100">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(answer.date)}</span>
              </div>
            </CardHeader>
            <CardContent className="p-8">
              {/* 答案 */}
              <div className="text-center mb-8">
                <h1 className="text-4xl font-bold text-gray-800 mb-4">
                  {answer.answer}
                </h1>
                <Badge variant="secondary" className="text-lg px-4 py-2">
                  今日答案
                </Badge>
              </div>

              {/* 线索词 */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-700 mb-4 text-center">
                  线索词
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  {answer.clues.map((clue, index) => (
                    <div
                      key={index}
                      className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white p-4 rounded-lg text-center font-medium shadow-md"
                    >
                      <div className="text-sm opacity-80 mb-1">线索 {index + 1}</div>
                      <div className="text-lg font-bold">{clue}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 分享按钮 */}
              <div className="flex justify-center gap-4">
                <Button onClick={handleShare} className="flex items-center gap-2">
                  <Share2 className="w-4 h-4" />
                  分享答案
                </Button>
                <Button
                  variant="outline"
                  onClick={handleCopyLink}
                  className="flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  {copied ? "已复制!" : "复制链接"}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 下一个谜题倒计时 */}
          <Card className="mb-8 shadow-lg">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2 text-xl">
                <Clock className="w-5 h-5" />
                下一个谜题发布倒计时
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <div className="text-4xl font-mono font-bold text-blue-600 mb-4">
                {countdown}
              </div>
              <p className="text-gray-600">
                新的Pinpoint谜题将在太平洋时间午夜发布
              </p>
            </CardContent>
          </Card>

          {/* 游戏说明 */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>关于 LinkedIn Pinpoint</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-600 mb-4">
                  LinkedIn Pinpoint 是一个每日文字游戏，玩家需要根据5个线索词猜出共同的主题或类别。
                </p>
                <h3 className="text-lg font-semibold mb-2">游戏规则：</h3>
                <ul className="list-disc list-inside text-gray-600 space-y-1 mb-4">
                  <li>每天发布一个新的谜题</li>
                  <li>根据5个线索词找出共同点</li>
                  <li>答案通常是一个类别、主题或概念</li>
                  <li>每个谜题都有唯一的编号</li>
                </ul>
                <div className="flex gap-4">
                  <Link href="/history">
                    <Button variant="outline">查看历史答案</Button>
                  </Link>
                  <Link href="/">
                    <Button>返回首页</Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
