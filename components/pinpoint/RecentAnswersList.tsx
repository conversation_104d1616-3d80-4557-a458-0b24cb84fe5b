import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Hash, ArrowRight } from "lucide-react";

interface RecentAnswersListProps {
  recentAnswers: PinpointAnswerFormatted[];
}

export default function RecentAnswersList({ recentAnswers }: RecentAnswersListProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="apple-card p-8">
      {/* Header */}
      <div className="mb-8">
        <h2 className="apple-subtitle mb-2">最近答案</h2>
        <p className="apple-body">查看最近发布的Pinpoint答案</p>
      </div>

      {/* Content */}
      {recentAnswers.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Hash className="w-8 h-8" style={{ color: 'var(--color-text-secondary)' }} />
          </div>
          <p className="apple-body">暂无最近答案</p>
        </div>
      ) : (
        <div className="space-y-3">
          {recentAnswers.map((answer, index) => (
            <Link
              key={answer.gameNumber}
              href={`/${answer.urlSlug}`}
              className="block group"
            >
              <div className="p-4 rounded-xl transition-all duration-300 hover:scale-[1.02]"
                   style={{
                     backgroundColor: 'var(--color-surface-secondary)',
                     border: '1px solid var(--color-border-light)'
                   }}>
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full flex items-center justify-center"
                             style={{ backgroundColor: 'var(--color-primary)' }}>
                          <Hash className="w-4 h-4 text-white" />
                        </div>
                        <span className="font-semibold apple-body" style={{ color: 'var(--color-primary)' }}>
                          #{answer.gameNumber}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 apple-caption">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(answer.date)}</span>
                      </div>
                      {index === 0 && (
                        <div className="px-2 py-1 rounded-full text-xs font-medium"
                             style={{
                               backgroundColor: 'var(--color-primary)',
                               color: 'white'
                             }}>
                          最新
                        </div>
                      )}
                    </div>

                    {/* Mystery Answer Hint */}
                    <div className="mb-3 flex items-center gap-2">
                      <span className="text-sm font-medium" style={{ color: 'var(--color-text-secondary)' }}>
                        🤔 猜猜答案是什么？
                      </span>
                      <div className="flex gap-1">
                        <span className="inline-block w-8 h-3 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
                        <span className="inline-block w-12 h-3 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
                        <span className="inline-block w-6 h-3 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
                      </div>
                    </div>

                    {/* Clues */}
                    <div className="space-y-2">
                      <p className="text-xs font-medium" style={{ color: 'var(--color-text-tertiary)' }}>
                        线索词:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {answer.clues.slice(0, 3).map((clue, clueIndex) => (
                          <span
                            key={clueIndex}
                            className="inline-block px-3 py-1 rounded-full text-xs font-medium"
                            style={{
                              backgroundColor: 'var(--color-primary)',
                              color: 'white'
                            }}
                          >
                            {clue}
                          </span>
                        ))}
                        {answer.clues.length > 3 && (
                          <span className="inline-block px-3 py-1 rounded-full text-xs font-medium"
                                style={{
                                  backgroundColor: 'var(--color-surface)',
                                  color: 'var(--color-text-secondary)',
                                  border: '1px solid var(--color-border)'
                                }}>
                            +{answer.clues.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="ml-4 transition-transform duration-300 group-hover:translate-x-1">
                    <ArrowRight className="w-5 h-5" style={{ color: 'var(--color-text-tertiary)' }} />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="mt-8 pt-6 border-t" style={{ borderColor: 'var(--color-border-light)' }}>
        <Link href="/history" className="apple-button apple-button-secondary w-full justify-center">
          查看所有历史答案
        </Link>
      </div>
    </div>
  );
}
