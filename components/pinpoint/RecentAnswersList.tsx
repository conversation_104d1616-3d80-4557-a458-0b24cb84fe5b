"use client";

import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, Hash, ArrowRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";

interface RecentAnswersListProps {
  recentAnswers: PinpointAnswerFormatted[];
}

export default function RecentAnswersList({ recentAnswers }: RecentAnswersListProps) {
  const t = useTranslations("pinpoint");
  const params = useParams();
  const locale = params.locale as string;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const localeCode = locale === "zh" ? "zh-CN" : "en-US";

    return date.toLocaleDateString(localeCode, {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="apple-card p-8">
      {/* Header */}
      <div className="mb-6">
        <h2 className="apple-subtitle mb-2">{t("recent_answers")}</h2>
        <p className="apple-body">{t("recent_answers_description")}</p>
      </div>

      {/* Content */}
      {recentAnswers.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               style={{ backgroundColor: 'var(--color-surface-secondary)' }}>
            <Hash className="w-8 h-8" style={{ color: 'var(--color-text-secondary)' }} />
          </div>
          <p className="apple-body">{t("no_results")}</p>
        </div>
      ) : (
        <div className="space-y-4">
          {recentAnswers.map((answer, index) => (
            <Link
              key={answer.gameNumber}
              href={`/${answer.urlSlug}`}
              className="block group"
            >
              <div className="p-5 rounded-xl transition-all duration-300 hover:scale-[1.02]"
                   style={{
                     backgroundColor: 'var(--color-surface-secondary)',
                     border: '1px solid var(--color-border-light)'
                   }}>
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center"
                             style={{ backgroundColor: 'var(--color-primary)' }}>
                          <Hash className="w-3 h-3 text-white" />
                        </div>
                        <span className="font-semibold text-sm" style={{ color: 'var(--color-primary)' }}>
                          {t("game_number", { number: answer.gameNumber })}
                        </span>
                      </div>
                      <div className="flex items-center gap-1 text-xs" style={{ color: 'var(--color-text-secondary)' }}>
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(answer.date)}</span>
                      </div>
                    </div>

                    {/* Mystery Answer Hint */}
                    <div className="mb-3 flex items-center gap-2">
                      <span className="text-xs font-medium" style={{ color: 'var(--color-text-secondary)' }}>
                        {t("guess_answer")}
                      </span>
                      <div className="flex gap-1">
                        <span className="inline-block w-6 h-2 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)' }}></span>
                        <span className="inline-block w-8 h-2 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.2s' }}></span>
                        <span className="inline-block w-4 h-2 rounded-sm animate-pulse"
                              style={{ backgroundColor: 'var(--color-surface-secondary)', animationDelay: '0.4s' }}></span>
                      </div>
                    </div>

                    {/* Clues - 只显示前3个 */}
                    <div className="flex flex-wrap gap-1">
                      {answer.clues.slice(0, 3).map((clue, clueIndex) => (
                        <span
                          key={clueIndex}
                          className="inline-block px-2 py-1 rounded-full text-xs font-medium"
                          style={{
                            backgroundColor: 'var(--color-primary)',
                            color: 'white'
                          }}
                        >
                          {clue}
                        </span>
                      ))}
                      {answer.clues.length > 3 && (
                        <span
                          className="inline-block px-2 py-1 rounded-full text-xs font-medium"
                          style={{
                            backgroundColor: 'var(--color-text-tertiary)',
                            color: 'white'
                          }}
                        >
                          +{answer.clues.length - 3}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="ml-3 transition-transform duration-300 group-hover:translate-x-1">
                    <ArrowRight className="w-4 h-4" style={{ color: 'var(--color-text-tertiary)' }} />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-6 border-t" style={{ borderColor: 'var(--color-border-light)' }}>
        <Link href="/history" className="apple-button apple-button-secondary w-full justify-center">
          {t("view_all_history")}
        </Link>
      </div>
    </div>
  );
}
