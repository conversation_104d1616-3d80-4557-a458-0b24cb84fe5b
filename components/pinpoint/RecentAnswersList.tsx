import Link from "next/link";
import { PinpointAnswerFormatted } from "@/types/pinpoint";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Hash, ArrowRight } from "lucide-react";

interface RecentAnswersListProps {
  recentAnswers: PinpointAnswerFormatted[];
}

export default function RecentAnswersList({ recentAnswers }: RecentAnswersListProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-gray-800">
          最近答案
        </CardTitle>
        <p className="text-gray-600">查看最近发布的Pinpoint答案</p>
      </CardHeader>
      <CardContent className="p-0">
        {recentAnswers.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            暂无最近答案
          </div>
        ) : (
          <div className="space-y-0">
            {recentAnswers.map((answer, index) => (
              <Link
                key={answer.gameNumber}
                href={`/${answer.urlSlug}`}
                className="block hover:bg-gray-50 transition-colors"
              >
                <div className="p-4 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="flex items-center gap-1 text-blue-600">
                          <Hash className="w-4 h-4" />
                          <span className="font-semibold">#{answer.gameNumber}</span>
                        </div>
                        <div className="flex items-center gap-1 text-gray-500 text-sm">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(answer.date)}</span>
                        </div>
                        {index === 0 && (
                          <Badge variant="secondary" className="text-xs">
                            最新
                          </Badge>
                        )}
                      </div>
                      
                      <h3 className="font-medium text-gray-800 mb-2 line-clamp-1">
                        {answer.answer}
                      </h3>
                      
                      <div className="flex flex-wrap gap-1">
                        {answer.clues.slice(0, 3).map((clue, clueIndex) => (
                          <span
                            key={clueIndex}
                            className="inline-block bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded"
                          >
                            {clue}
                          </span>
                        ))}
                        {answer.clues.length > 3 && (
                          <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                            +{answer.clues.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="ml-4">
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
        
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <Link href="/history">
            <Button variant="outline" className="w-full">
              查看所有历史答案
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
